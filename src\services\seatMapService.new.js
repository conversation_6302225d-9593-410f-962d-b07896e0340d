import axios from "axios";
import { getAuthToken } from "./apiService";

/**
 * Service for handling seat map operations, pricing, and validations
 */
export class SeatMapService {
  // Fixed prices for different seat types
  static SEAT_PRICES = {
    premium: 50,
    exit: 30,
    standard: 15,
    window: 5,
    aisle: 5,
    extraLegroom: 20
  };

  /**
   * Fetch seat map for a flight from Amadeus API
   */
  static async getSeatMap(flightOfferId) {
    try {
      const token = await getAuthToken();
      const response = await axios.get(
        `${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v1/shopping/seatmaps`,
        {
          headers: { Authorization: `Bearer ${token}` },
          params: { "flight-orderId": flightOfferId }
        }
      );

      // Return mock data in development if API returns no data
      if (import.meta.env.DEV && (!response.data?.data || response.data.data.length === 0)) {
        return this.getMockSeatMap();
      }

      // Transform Amadeus seat map data to our format
      return this.transformAmadeusSeatMap(response.data.data[0]);
    } catch (error) {
      console.error("Failed to fetch seat map:", error);
      if (import.meta.env.DEV) {
        return this.getMockSeatMap();
      }
      throw new Error("Failed to fetch seat map data");
    }
  }

  /**
   * Transform Amadeus seat map data to our format
   */
  static transformAmadeusSeatMap(amadeusData) {
    const seatMap = [];
    
    // Get cabin layout
    const deck = amadeusData.decks[0];
    const rows = deck.rows || [];

    rows.forEach((row) => {
      const seatRow = [];
      row.seats.forEach((seat) => {
        if (seat.number) {
          const [rowNum, letter] = this.parseSeatNumber(seat.number);
          seatRow.push({
            id: seat.number,
            number: seat.number,
            available: seat.travelerPricing && seat.travelerPricing.length > 0,
            type: this.getSeatType(rowNum),
            features: this.getSeatFeatures(rowNum, letter),
            price: this.calculateSeatPrice(rowNum, letter.charCodeAt(0) - 65),
            characteristics: seat.characteristicsCodes || []
          });
        } else {
          // Handle aisle
          seatRow.push({
            id: `aisle-${row.number}`,
            type: "AISLE",
            available: false
          });
        }
      });
      seatMap.push(seatRow);
    });

    return seatMap;
  }

  /**
   * Generate mock seat map data for development
   */
  static getMockSeatMap() {
    const rows = 30;
    const seatsPerRow = 6;
    const seatMap = [];

    for (let row = 1; row <= rows; row++) {
      const seatRow = [];
      for (let seat = 0; seat < seatsPerRow; seat++) {
        const seatLetter = String.fromCharCode(65 + seat);
        const seatNumber = `${row}${seatLetter}`;
        seatRow.push({
          id: seatNumber,
          number: seatNumber,
          // More realistic availability pattern
          available: this.getMockAvailability(row, seat),
          type: this.getSeatType(row),
          features: this.getSeatFeatures(row, seatLetter),
          price: this.calculateSeatPrice(row, seat)
        });
      }
      // Add aisle in the middle
      seatRow.push({ 
        id: `aisle-${row}`, 
        type: "AISLE", 
        available: false 
      });
      seatMap.push(seatRow);
    }
    return seatMap;
  }

  /**
   * Generate realistic mock availability
   */
  static getMockAvailability(row, seat) {
    // Premium rows (1-5) are more likely to be taken
    if (row <= 5) {
      return Math.random() > 0.7;
    }
    // Exit rows (12-14) are also popular
    if (row >= 12 && row <= 14) {
      return Math.random() > 0.6;
    }
    // Window and aisle seats are preferred
    if (seat === 0 || seat === 5 || seat === 2 || seat === 3) {
      return Math.random() > 0.5;
    }
    // Middle seats are least popular
    return Math.random() > 0.3;
  }

  /**
   * Get seat type based on row number
   */
  static getSeatType(row) {
    if (row <= 5) return "PREMIUM";
    if (row >= 12 && row <= 14) return "EXIT";
    return "STANDARD";
  }

  /**
   * Get seat features based on position
   */
  static getSeatFeatures(row, letter) {
    const features = [];
    // Window seats
    if (letter === 'A' || letter === 'F') features.push("WINDOW");
    // Aisle seats
    if (letter === 'C' || letter === 'D') features.push("AISLE");
    // Extra legroom in first row
    if (row === 1) features.push("EXTRA_LEGROOM");
    // Premium cabin features
    if (row <= 5) features.push("PREMIUM_SEAT");
    // Exit row features
    if (row >= 12 && row <= 14) features.push("EXIT_ROW");
    return features;
  }

  /**
   * Calculate seat price based on location and features
   */
  static calculateSeatPrice(row, seat) {
    let price = this.SEAT_PRICES.standard;

    // Premium cabin pricing
    if (row <= 5) {
      price = this.SEAT_PRICES.premium;
      if (row === 1) price += this.SEAT_PRICES.extraLegroom;
    }
    // Exit row pricing
    else if (row >= 12 && row <= 14) {
      price = this.SEAT_PRICES.exit;
    }

    // Add window/aisle premiums
    if (seat === 0 || seat === 5) price += this.SEAT_PRICES.window;
    if (seat === 2 || seat === 3) price += this.SEAT_PRICES.aisle;

    return price;
  }

  /**
   * Validate seat selection against all rules
   */
  static validateSeatSelection(passenger, seat, selectedSeats) {
    const validationErrors = [];

    // Basic availability check
    if (!seat?.available) {
      validationErrors.push(`Seat ${seat.number} is not available`);
      return validationErrors;
    }

    // Exit row restrictions
    if (seat.type === "EXIT") {
      if (passenger.type === "INFANT" || passenger.type === "CHILD") {
        validationErrors.push("Exit row seats are not available for infants and children");
      }
      if (passenger.specialAssistance) {
        validationErrors.push("Exit row seats are not available for passengers requiring special assistance");
      }
    }

    // Infant seating rules
    if (passenger.type === "INFANT") {
      // No exit rows for infants
      if (seat.type === "EXIT") {
        validationErrors.push("Infants cannot be seated in exit rows");
      }

      // Must be adjacent to associated adult
      const adultSeat = Object.entries(selectedSeats).find(
        ([_, id]) => id === passenger.associatedAdult
      )?.[0];

      if (adultSeat && !this.areSeatsAdjacent(seat.number, adultSeat)) {
        validationErrors.push(
          "Infant must be seated adjacent to their associated adult"
        );
      }

      // Limit infants in premium cabin
      if (seat.type === "PREMIUM") {
        const premiumInfantCount = Object.entries(selectedSeats).filter(([seatNum, id]) => {
          const passengerType = passenger.type === "INFANT" ? "INFANT" : "OTHER";
          return this.getSeatType(parseInt(seatNum)) === "PREMIUM" && 
                 passengerType === "INFANT";
        }).length;

        if (premiumInfantCount >= 1) {
          validationErrors.push("Maximum of 1 infant allowed in premium cabin");
        }
      }
    }

    // Special assistance passenger rules
    if (passenger.specialAssistance) {
      if (!seat.features.includes("AISLE")) {
        validationErrors.push(
          "Passengers requiring special assistance should be seated in aisle seats"
        );
      }
    }

    return validationErrors;
  }

  /**
   * Check if two seats are adjacent
   */
  static areSeatsAdjacent(seat1, seat2) {
    if (!seat1 || !seat2) return false;

    const [row1, letter1] = this.parseSeatNumber(seat1);
    const [row2, letter2] = this.parseSeatNumber(seat2);

    // Must be in same row
    if (row1 !== row2) return false;

    // Check if letters are adjacent
    const letter1Code = letter1.charCodeAt(0);
    const letter2Code = letter2.charCodeAt(0);

    // Consider seats across the aisle to be adjacent (C-D)
    if ((letter1 === 'C' && letter2 === 'D') || 
        (letter1 === 'D' && letter2 === 'C')) {
      return true;
    }

    return Math.abs(letter1Code - letter2Code) === 1;
  }

  /**
   * Parse seat number into row and letter
   */
  static parseSeatNumber(seatNumber) {
    const matches = seatNumber.match(/(\d+)([A-F])/);
    if (!matches) return [0, ''];
    return [parseInt(matches[1]), matches[2]];
  }
}
