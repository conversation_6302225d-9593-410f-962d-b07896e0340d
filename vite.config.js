import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@components': path.resolve(__dirname, './src/Components'),
      '@features': path.resolve(__dirname, './src/features')
    }
  },
  server: {
    host: true, // Expose to all network interfaces
    port: 5173,
    strictPort: true,
    hmr: {
      timeout: 5000,
      overlay: true,
      clientPort: 5173,
      protocol: 'ws'
    }
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true
  }
});
