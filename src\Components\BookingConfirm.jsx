import React, { useState, useRef, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { formatAirportDisplay } from "../utils/airportUtils";
import { formatDuration, formatDate } from "../utils/formatUtils";
import { getBookingByPNR } from "../services/apiService";
import BookingModification from "./BookingModification";
import "../assets/styles/BookingConfirm.css";

const BookingConfirm = () => {
  const [isPrinting, setIsPrinting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [bookingDetails, setBookingDetails] = useState(null);
  const [pnrCopied, setPnrCopied] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const printRef = useRef();

  // Destructure with default values
  const {
    bookingData = {},
    selectedFlights = {},
    passengerData = [],
    totalAmount = 0,
    pricingOption = "basic",
    passengers = 1,
    children = 0,
    infants = 0,
    fromPNRLookup = false,
  } = location.state || {};

  // Extract PNR from booking data
  const pnr =
    bookingData?.pnr ||
    bookingData?.data?.associatedRecords?.[0]?.reference ||
    bookingData?.data?.id ||
    `TS-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

  // Format booking date
  const bookingDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  // If this component is accessed via PNR lookup, fetch the booking details
  useEffect(() => {
    // Log the received state for debugging
    console.log("BookingConfirm received state:", location.state);

    if (fromPNRLookup && pnr) {
      const fetchBookingDetails = async () => {
        setLoading(true);
        setError(null);

        try {
          // Fetch booking details using the PNR
          const details = await getBookingByPNR(pnr);
          setBookingDetails(details);
          console.log("Fetched booking details:", details);
        } catch (err) {
          console.error("Error fetching booking details:", err);
          setError(
            "Unable to retrieve booking details. Please try again later."
          );
        } finally {
          setLoading(false);
        }
      };

      fetchBookingDetails();
    } else {
      // For new bookings, validate that we have the necessary data
      if (!bookingData || !selectedFlights?.outbound) {
        setError("Invalid booking data provided");
        console.error("Missing booking data:", {
          bookingData,
          selectedFlights,
        });
      }
    }
  }, [fromPNRLookup, pnr, bookingData, selectedFlights]);
  // Handle print ticket
  const handlePrint = () => {
    setIsPrinting(true);
    setTimeout(() => {
      if (printRef.current) {
        const content = printRef.current.innerHTML;
        const printWindow = window.open("", "_blank");
        printWindow.document.write(`
          <html>
            <head>
              <title>Booking Confirmation - ${pnr}</title>
              <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
              <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
              <style>
                body { padding: 20px; }
                .booking-confirmation { max-width: 800px; margin: 0 auto; }
                .pnr-display {
                  border: 2px solid #198754;
                  padding: 20px;
                  margin: 20px 0;
                  text-align: center;
                }
                .pnr-code {
                  font-family: monospace;
                  font-size: 24px;
                  font-weight: bold;
                  margin: 10px 0;
                }
                @media print {
                  .no-print { display: none !important; }
                }
              </style>
            </head>
            <body>
              <div class="booking-confirmation">
                ${content}
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
          setIsPrinting(false);
        }, 500);
      }
    }, 100);
  };

  // Handle copy PNR to clipboard
  const handleCopyPNR = async () => {
    try {
      await navigator.clipboard.writeText(pnr);
      setPnrCopied(true);
      setTimeout(() => setPnrCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy PNR:", err);
    }
  };

  // Reset copied state when PNR changes
  useEffect(() => {
    setPnrCopied(false);
  }, [pnr]);

  // Show loading state
  if (loading) {
    return (
      <div className="container p-4 text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-3">Retrieving booking details...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container p-4">
        <div className="alert alert-danger">
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </div>
        <button className="btn btn-primary mt-3" onClick={() => navigate("/")}>
          Back to Home
        </button>
      </div>
    );
  }

  // For PNR lookup, we might have different data structure
  // We'll use this data structure in future enhancements

  // Add prop validation - if not from PNR lookup, we need flight data
  if (!fromPNRLookup && !selectedFlights?.outbound) {
    return (
      <div className="container p-4">
        <div className="alert alert-danger">
          <i className="fas fa-exclamation-circle me-2"></i>
          No booking information provided. Please start your booking again.
        </div>
        <button className="btn btn-primary mt-3" onClick={() => navigate("/")}>
          Start New Search
        </button>
      </div>
    );
  }

  // If from PNR lookup but no booking details found
  if (fromPNRLookup && !bookingDetails) {
    return (
      <div className="container p-4">
        <div className="alert alert-warning">
          <i className="fas fa-exclamation-triangle me-2"></i>
          Unable to retrieve complete booking details for PNR: {pnr}
        </div>
        <button className="btn btn-primary mt-3" onClick={() => navigate("/")}>
          Back to Home
        </button>
      </div>
    );
  }

  // Get flight details
  const getFlightDetails = (flight) => {
    if (!flight?.itineraries?.[0]?.segments) return null;

    const segments = flight.itineraries[0].segments;
    const firstSegment = segments[0];
    const lastSegment = segments[segments.length - 1];

    return {
      departureCode: firstSegment.departure.iataCode,
      departureCity: formatAirportDisplay(
        firstSegment.departure.iataCode,
        "city"
      ),
      departureCountry: formatAirportDisplay(
        firstSegment.departure.iataCode,
        "country"
      ),
      departureTime: new Date(firstSegment.departure.at).toLocaleTimeString(
        [],
        {
          hour: "2-digit",
          minute: "2-digit",
        }
      ),
      departureDate: formatDate(firstSegment.departure.at),

      arrivalCode: lastSegment.arrival.iataCode,
      arrivalCity: formatAirportDisplay(lastSegment.arrival.iataCode, "city"),
      arrivalCountry: formatAirportDisplay(
        lastSegment.arrival.iataCode,
        "country"
      ),
      arrivalTime: new Date(lastSegment.arrival.at).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      }),
      arrivalDate: formatDate(lastSegment.arrival.at),

      duration: formatDuration(flight.itineraries[0].duration),
      stops: segments.length - 1,
      flightNumber: `${firstSegment.carrierCode}${firstSegment.number}`,
      price: `${flight.price.currency} ${flight.price.total}`,
    };
  };

  const outboundFlight = getFlightDetails(selectedFlights.outbound);
  const returnFlight = selectedFlights.return
    ? getFlightDetails(selectedFlights.return)
    : null;

  const renderPriceBreakdown = () => {
    const priceDetails = bookingData?.priceDetails || {};
    return (
      <div className="price-breakdown card mb-4">
        <div className="card-header bg-light">
          <h4 className="mb-0">
            Price Breakdown
            {priceDetails.isPromoValid && (
              <span className="promo-badge">
                <i className="fas fa-tag me-1"></i>
                Promo Applied
              </span>
            )}
          </h4>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-borderless">
              <tbody>
                {/* Base Fare Section */}
                <tr>
                  <td colSpan="2">
                    <strong>Base Fare Breakdown</strong>
                  </td>
                </tr>
                <tr>
                  <td>Base Flight Price</td>
                  <td className="text-end">
                    {priceDetails.currency}{" "}
                    {priceDetails.basePrice?.toFixed(2) || 0}
                  </td>
                </tr>
                <tr>
                  <td>Fare Type ({pricingOption})</td>
                  <td className="text-end">
                    x{priceDetails.fareMultiplier || 1}
                  </td>
                </tr>
                <tr className="border-top">
                  <td>Base Fare Subtotal</td>
                  <td className="text-end">
                    {priceDetails.currency}{" "}
                    {(
                      priceDetails.basePrice *
                      (priceDetails.fareMultiplier || 1)
                    ).toFixed(2)}
                  </td>
                </tr>

                {/* Passenger Breakdown */}
                <tr className="mt-3">
                  <td colSpan="2">
                    <strong>Passenger Breakdown</strong>
                  </td>
                </tr>
                {passengers > 0 && (
                  <tr>
                    <td>Adults ({passengers}x)</td>
                    <td className="text-end">
                      {priceDetails.currency}{" "}
                      {(
                        priceDetails.basePrice *
                        passengers *
                        (priceDetails.fareMultiplier || 1)
                      ).toFixed(2)}
                    </td>
                  </tr>
                )}
                {children > 0 && (
                  <tr>
                    <td>Children ({children}x)</td>
                    <td className="text-end">
                      {priceDetails.currency}{" "}
                      {(
                        priceDetails.basePrice *
                        children *
                        0.75 *
                        (priceDetails.fareMultiplier || 1)
                      ).toFixed(2)}
                    </td>
                  </tr>
                )}
                {infants > 0 && (
                  <tr>
                    <td>Infants ({infants}x)</td>
                    <td className="text-end">
                      {priceDetails.currency}{" "}
                      {(
                        priceDetails.basePrice *
                        infants *
                        0.1 *
                        (priceDetails.fareMultiplier || 1)
                      ).toFixed(2)}
                    </td>
                  </tr>
                )}
                <tr className="border-top">
                  <td>Passenger Subtotal</td>
                  <td className="text-end">
                    {priceDetails.currency}{" "}
                    {priceDetails.passengerTotal?.toFixed(2) || 0}
                  </td>
                </tr>

                {/* Seat Selection Section */}
                {seatData && seatData.assignments && (
                  <>
                    <tr className="mt-3">
                      <td colSpan="2">
                        <strong>Seat Selection Charges</strong>
                      </td>
                    </tr>
                    {Object.entries(seatData.assignments).map(
                      ([flightType, seats]) =>
                        seats.length > 0 && (
                          <tr key={flightType}>
                            <td>
                              {flightType.charAt(0).toUpperCase() +
                                flightType.slice(1)}{" "}
                              Flight Seats ({seats.length})
                            </td>
                            <td className="text-end">
                              {priceDetails.currency}{" "}
                              {(
                                priceDetails.seatTotal /
                                (seatData.assignments.return?.length ? 2 : 1)
                              ).toFixed(2)}
                            </td>
                          </tr>
                        )
                    )}
                    <tr className="border-top">
                      <td>Seat Selection Subtotal</td>
                      <td className="text-end">
                        {priceDetails.currency}{" "}
                        {priceDetails.seatTotal?.toFixed(2) || 0}
                      </td>
                    </tr>
                  </>
                )}

                {/* Subtotal Before Discount */}
                <tr className="border-top">
                  <td>
                    <strong>Subtotal</strong>
                  </td>
                  <td className="text-end">
                    <strong>
                      {priceDetails.currency}{" "}
                      {priceDetails.subtotal?.toFixed(2) || 0}
                    </strong>
                  </td>
                </tr>

                {/* Promotional Discount Section */}
                {priceDetails.promoDiscount > 0 && (
                  <>
                    <tr>
                      <td>
                        <span className="promotional-discount">
                          <i className="fas fa-tag me-1"></i>
                          Promotional Discount
                        </span>
                        <br />
                        <small className="text-muted">
                          {priceDetails.promoMessage}
                        </small>
                      </td>
                      <td className="text-end promotional-discount">
                        -{priceDetails.currency}{" "}
                        {priceDetails.promoDiscount.toFixed(2)}
                      </td>
                    </tr>
                  </>
                )}

                {/* Final Total */}
                <tr className="border-top border-2">
                  <td>
                    <strong>Total Amount</strong>
                  </td>
                  <td className="text-end">
                    <strong>
                      {priceDetails.currency}{" "}
                      {priceDetails.total?.toFixed(2) || 0}
                    </strong>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container my-4" ref={printRef}>
      <div className="card shadow-sm">
        <div className="card-header bg-success text-white">
          <div className="d-flex justify-content-between align-items-center">
            <h3 className="mb-0">Booking Confirmed!</h3>
            <div className="booking-date">{bookingDate}</div>
          </div>
        </div>

        <div className="card-body">
          <div className="alert alert-success mb-4">
            <i className="fas fa-check-circle me-2"></i>
            Your flight booking has been confirmed. Thank you for choosing
            Tripstar!
          </div>

          <div className="pnr-display text-center">
            <h4 className="text-success mb-3">
              <i className="fas fa-ticket-alt me-2"></i>
              Booking Reference (PNR)
            </h4>
            <div
              className="pnr-code"
              onClick={handleCopyPNR}
              title="Click to copy"
            >
              {pnr}
            </div>
            <button
              className={`btn ${pnrCopied ? "btn-success" : "btn-outline-primary"} pnr-copy-button`}
              onClick={handleCopyPNR}
            >
              {pnrCopied ? (
                <>
                  <i className="fas fa-check me-2"></i>
                  Copied!
                </>
              ) : (
                <>
                  <i className="fas fa-copy me-2"></i>
                  Copy Booking Reference
                </>
              )}
            </button>
            <p className="text-muted mt-3 mb-0">
              <i className="fas fa-info-circle me-2"></i>
              Please save this reference number for future inquiries
            </p>
          </div>

          <div className="booking-details mb-4">
            <h4>Booking Details</h4>
            <div className="row">
              <div className="col-md-6">
                <p>
                  <strong>Booking Date:</strong> {bookingDate}
                </p>
                <p>
                  <strong>Booking Status:</strong>{" "}
                  <span className="text-success">Confirmed</span>
                </p>
              </div>
              <div className="col-md-6">
                <div>
                  <p>
                    <strong>Passengers:</strong>
                  </p>
                  <ul className="list-unstyled ms-3">
                    <li>Adults: {passengers}</li>
                    {children > 0 && <li>Children: {children}</li>}
                    {infants > 0 && <li>Infants: {infants}</li>}
                    <li>
                      <strong>Total: {passengerData.length}</strong>
                    </li>
                  </ul>
                </div>
                <p>
                  <strong>Fare Type:</strong>{" "}
                  {pricingOption === "flex"
                    ? "Flexible (Refundable)"
                    : "Basic (Non-refundable)"}
                </p>
              </div>
            </div>
          </div>

          <div className="flight-details mb-4">
            <h4>Flight Details</h4>

            {/* Outbound Flight */}
            <div className="card mb-3">
              <div className="card-header bg-primary text-white">
                <h5 className="mb-0">Outbound Flight</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-5">
                    <div className="departure-details">
                      <h5>
                        {outboundFlight.departureCode} -{" "}
                        {outboundFlight.departureCity},{" "}
                        {outboundFlight.departureCountry}
                      </h5>
                      <p>{outboundFlight.departureDate}</p>
                      <p className="fs-4">{outboundFlight.departureTime}</p>
                    </div>
                  </div>

                  <div className="col-md-2 text-center">
                    <div className="flight-path">
                      <div className="duration">{outboundFlight.duration}</div>
                      <div className="path-line"></div>
                      <div className="stops">
                        {outboundFlight.stops === 0
                          ? "Direct"
                          : `${outboundFlight.stops} Stop${
                              outboundFlight.stops > 1 ? "s" : ""
                            }`}
                      </div>
                    </div>
                  </div>

                  <div className="col-md-5">
                    <div className="arrival-details text-end">
                      <h5>
                        {outboundFlight.arrivalCode} -{" "}
                        {outboundFlight.arrivalCity},{" "}
                        {outboundFlight.arrivalCountry}
                      </h5>
                      <p>{outboundFlight.arrivalDate}</p>
                      <p className="fs-4">{outboundFlight.arrivalTime}</p>
                    </div>
                  </div>
                </div>

                <div className="flight-info mt-3">
                  <p>
                    <strong>Flight:</strong> {outboundFlight.flightNumber}
                  </p>
                </div>
              </div>
            </div>

            {/* Return Flight (if applicable) */}
            {returnFlight && (
              <div className="card">
                <div className="card-header bg-primary text-white">
                  <h5 className="mb-0">Return Flight</h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-5">
                      <div className="departure-details">
                        <h5>
                          {returnFlight.departureCode} -{" "}
                          {returnFlight.departureCity},{" "}
                          {returnFlight.departureCountry}
                        </h5>
                        <p>{returnFlight.departureDate}</p>
                        <p className="fs-4">{returnFlight.departureTime}</p>
                      </div>
                    </div>

                    <div className="col-md-2 text-center">
                      <div className="flight-path">
                        <div className="duration">{returnFlight.duration}</div>
                        <div className="path-line"></div>
                        <div className="stops">
                          {returnFlight.stops === 0
                            ? "Direct"
                            : `${returnFlight.stops} Stop${
                                returnFlight.stops > 1 ? "s" : ""
                              }`}
                        </div>
                      </div>
                    </div>

                    <div className="col-md-5">
                      <div className="arrival-details text-end">
                        <h5>
                          {returnFlight.arrivalCode} -{" "}
                          {returnFlight.arrivalCity},{" "}
                          {returnFlight.arrivalCountry}
                        </h5>
                        <p>{returnFlight.arrivalDate}</p>
                        <p className="fs-4">{returnFlight.arrivalTime}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flight-info mt-3">
                    <p>
                      <strong>Flight:</strong> {returnFlight.flightNumber}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="passenger-details mb-4">
            <h4>Passenger Details</h4>
            <div className="table-responsive">
              <table className="table table-striped">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Passport</th>
                    <th>Date of Birth</th>
                  </tr>
                </thead>
                <tbody>
                  {passengerData.map((passenger, index) => (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>
                        {passenger.firstName} {passenger.lastName}
                      </td>
                      <td>
                        <span
                          className={`badge ${
                            passenger.type === "ADULT"
                              ? "bg-primary"
                              : passenger.type === "CHILD"
                                ? "bg-info"
                                : "bg-warning text-dark"
                          }`}
                        >
                          {passenger.type || "Adult"}
                          {passenger.type === "INFANT" &&
                            passenger.associatedAdult &&
                            ` (with Adult ${passenger.associatedAdult})`}
                        </span>
                      </td>
                      <td>{passenger.passport}</td>
                      <td>{new Date(passenger.dob).toLocaleDateString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="payment-details">
            <h4>Payment Details</h4>
            <div className="row">
              <div className="col-md-6">
                <p>
                  <strong>Payment Method:</strong> Credit Card
                </p>
                <p>
                  <strong>Payment Status:</strong>{" "}
                  <span className="text-success">Paid</span>
                </p>
              </div>
              <div className="col-md-6 text-end">
                <p>
                  <strong>Total Amount:</strong>
                </p>
                <p className="total-amount">
                  {selectedFlights.outbound.price.currency}{" "}
                  {totalAmount.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          {/* Add Booking Modification Section */}
          <div className="booking-actions mt-4">
            <div className="card">
              <div className="card-header">
                <h4 className="mb-0">Manage Booking</h4>
              </div>
              <div className="card-body">
                <BookingModification
                  booking={{
                    pnr,
                    ...bookingData,
                    passengerData,
                    selectedFlights,
                    pricingOption,
                  }}
                  onModificationComplete={(updatedBooking) => {
                    // Update the booking details in state
                    if (fromPNRLookup) {
                      setBookingDetails(updatedBooking);
                    } else {
                      setBookingData(updatedBooking);
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="actions mt-4 d-flex justify-content-between">
        <button className="btn btn-secondary" onClick={() => navigate("/")}>
          Back to Home
        </button>

        <div>
          <button
            className="btn btn-primary me-2"
            onClick={handlePrint}
            disabled={isPrinting}
          >
            {isPrinting ? "Printing..." : "Print Ticket"}
          </button>

          <button
            className="btn btn-success"
            onClick={() => {
              // In a real app, this would send an email with the ticket
              alert("E-ticket has been sent to your email address.");
            }}
          >
            Email E-Ticket
          </button>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirm;
