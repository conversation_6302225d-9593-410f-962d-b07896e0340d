import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useAppContext, appActions } from "../../context/AppContext";
import "../../assets/styles/Navbar.css";

const Navbar = () => {
  const { state, dispatch } = useAppContext();
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);


  // Handle currency change
  const handleCurrencyChange = (currency) => {
    dispatch(appActions.setCurrency(currency));
    setShowCurrencyDropdown(false);
  };

  return (
    <>
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary">
        <div className="container">
          <Link className="navbar-brand" to="/">
            Tripstar
          </Link>

          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav me-auto">
              <li className="nav-item">
                <Link className="nav-link" to="/">
                  Home
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" to="/flights">
                  Flights
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" to="/hotels">
                  Hotels
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" to="/contact">
                  Contact
                </Link>
              </li>
            </ul>

            <ul className="navbar-nav">
              {/* Recent Searches Dropdown */}
              {state.recentSearches.length > 0 && (
                <li className="nav-item dropdown">
                  <a
                    className="nav-link dropdown-toggle"
                    href="#"
                    role="button"
                    data-bs-toggle="dropdown"
                  >
                    <i className="fas fa-history me-1"></i> Recent
                  </a>
                  <ul className="dropdown-menu dropdown-menu-end">
                    {state.recentSearches.map((search, index) => (
                      <li key={index}>
                        <Link
                          className="dropdown-item"
                          to="/flights"
                          state={search}
                        >
                          {search.origin} → {search.destination}
                        </Link>
                      </li>
                    ))}
                    <li>
                      <hr className="dropdown-divider" />
                    </li>
                    <li>
                      <button
                        className="dropdown-item text-danger"
                        onClick={() =>
                          dispatch(appActions.clearRecentSearches())
                        }
                      >
                        Clear All
                      </button>
                    </li>
                  </ul>
                </li>
              )}

              {/* Currency Selector */}
              {/* Removed the currency selector dropdown */}


              {/* Retrieve Booking */}
              <li className="nav-item">
                <Link className="nav-link" to="/retrieve-booking">
                  <i className="fas fa-search me-1"></i> Retrieve Booking
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
