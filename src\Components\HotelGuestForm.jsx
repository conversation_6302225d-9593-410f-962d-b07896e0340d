import React, { useState, useEffect } from 'react';
import { Form, Card, Row, Col, Button, Alert, Badge } from 'react-bootstrap';
import { <PERSON>a<PERSON>ser, FaEnvelope, FaPhone, FaCalendarAlt, FaPlus, FaTrash } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const HotelGuestForm = ({ 
  hotelOffer, 
  selectedRooms, 
  searchParams, 
  onGuestDetailsSubmit, 
  onBack 
}) => {
  const [guests, setGuests] = useState([]);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const hotel = hotelOffer?.hotel;
  const totalRooms = selectedRooms?.reduce((sum, room) => sum + (room.quantity || 1), 0) || 1;
  const totalGuests = searchParams?.adults || 1;

  // Initialize guests based on search parameters
  useEffect(() => {
    const initialGuests = [];
    for (let i = 0; i < totalGuests; i++) {
      initialGuests.push({
        id: i + 1,
        title: 'MR',
        firstName: '',
        lastName: '',
        email: i === 0 ? '' : '', // Only first guest needs email
        phone: i === 0 ? '' : '', // Only first guest needs phone
        dateOfBirth: null,
        specialRequests: '',
        isMainGuest: i === 0
      });
    }
    setGuests(initialGuests);
  }, [totalGuests]);

  // Handle input changes
  const handleInputChange = (guestIndex, field, value) => {
    const updatedGuests = [...guests];
    updatedGuests[guestIndex] = {
      ...updatedGuests[guestIndex],
      [field]: value
    };
    setGuests(updatedGuests);

    // Clear error for this field
    if (errors[`guest${guestIndex}_${field}`]) {
      const newErrors = { ...errors };
      delete newErrors[`guest${guestIndex}_${field}`];
      setErrors(newErrors);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    guests.forEach((guest, index) => {
      // Required fields for all guests
      if (!guest.firstName.trim()) {
        newErrors[`guest${index}_firstName`] = 'First name is required';
      }
      if (!guest.lastName.trim()) {
        newErrors[`guest${index}_lastName`] = 'Last name is required';
      }
      if (!guest.dateOfBirth) {
        newErrors[`guest${index}_dateOfBirth`] = 'Date of birth is required';
      } else {
        // Check if guest is at least 18 years old for main guest
        const today = new Date();
        const birthDate = new Date(guest.dateOfBirth);
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }

        if (guest.isMainGuest && age < 18) {
          newErrors[`guest${index}_dateOfBirth`] = 'Main guest must be at least 18 years old';
        }
      }

      // Required fields for main guest only
      if (guest.isMainGuest) {
        if (!guest.email.trim()) {
          newErrors[`guest${index}_email`] = 'Email is required for main guest';
        } else if (!/\S+@\S+\.\S+/.test(guest.email)) {
          newErrors[`guest${index}_email`] = 'Please enter a valid email address';
        }

        if (!guest.phone.trim()) {
          newErrors[`guest${index}_phone`] = 'Phone number is required for main guest';
        } else if (!/^\+?[\d\s-()]+$/.test(guest.phone)) {
          newErrors[`guest${index}_phone`] = 'Please enter a valid phone number';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format guest data for API
      const formattedGuests = guests.map(guest => ({
        title: guest.title,
        firstName: guest.firstName.trim(),
        lastName: guest.lastName.trim(),
        email: guest.email.trim() || guests[0].email.trim(), // Use main guest email for all
        phone: guest.phone.trim() || guests[0].phone.trim(), // Use main guest phone for all
        dateOfBirth: guest.dateOfBirth.toISOString().split('T')[0],
        specialRequests: guest.specialRequests.trim() || undefined
      }));

      if (onGuestDetailsSubmit) {
        await onGuestDetailsSubmit(formattedGuests);
      }
    } catch (error) {
      console.error('Guest details submission failed:', error);
      setErrors({ submit: 'Failed to submit guest details. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate total price
  const calculateTotalPrice = () => {
    if (!selectedRooms) return 0;
    return selectedRooms.reduce((total, room) => {
      return total + (parseFloat(room.price?.total || 0) * (room.quantity || 1));
    }, 0);
  };

  return (
    <div className="hotel-guest-form">
      {/* Booking Summary */}
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Booking Summary</h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={8}>
              <h6>{hotel?.name}</h6>
              <p className="text-muted mb-2">
                {hotel?.address?.cityName}, {hotel?.address?.countryCode}
              </p>
              <div className="booking-details">
                <small className="text-muted">
                  <strong>Check-in:</strong> {searchParams?.checkInDate} | 
                  <strong> Check-out:</strong> {searchParams?.checkOutDate} | 
                  <strong> Guests:</strong> {totalGuests} | 
                  <strong> Rooms:</strong> {totalRooms}
                </small>
              </div>
            </Col>
            <Col md={4} className="text-end">
              <h5 className="text-primary mb-0">
                ৳{calculateTotalPrice().toLocaleString()}
              </h5>
              <small className="text-muted">Total Amount</small>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Guest Details Form */}
      <Form onSubmit={handleSubmit}>
        <Card className="mb-4">
          <Card.Header>
            <h5 className="mb-0">Guest Details</h5>
            <small className="text-muted">
              Please provide details for all guests
            </small>
          </Card.Header>
          <Card.Body>
            {errors.submit && (
              <Alert variant="danger" className="mb-3">
                {errors.submit}
              </Alert>
            )}

            {guests.map((guest, index) => (
              <div key={guest.id} className="guest-section mb-4">
                <div className="d-flex align-items-center mb-3">
                  <h6 className="mb-0">
                    <FaUser className="me-2" />
                    Guest {index + 1}
                    {guest.isMainGuest && (
                      <Badge bg="primary" className="ms-2">Main Guest</Badge>
                    )}
                  </h6>
                </div>

                <Row className="g-3">
                  {/* Title */}
                  <Col md={2}>
                    <Form.Group>
                      <Form.Label>Title</Form.Label>
                      <Form.Select
                        value={guest.title}
                        onChange={(e) => handleInputChange(index, 'title', e.target.value)}
                      >
                        <option value="MR">Mr.</option>
                        <option value="MRS">Mrs.</option>
                        <option value="MS">Ms.</option>
                        <option value="DR">Dr.</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* First Name */}
                  <Col md={5}>
                    <Form.Group>
                      <Form.Label>First Name *</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter first name"
                        value={guest.firstName}
                        onChange={(e) => handleInputChange(index, 'firstName', e.target.value)}
                        isInvalid={!!errors[`guest${index}_firstName`]}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors[`guest${index}_firstName`]}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>

                  {/* Last Name */}
                  <Col md={5}>
                    <Form.Group>
                      <Form.Label>Last Name *</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter last name"
                        value={guest.lastName}
                        onChange={(e) => handleInputChange(index, 'lastName', e.target.value)}
                        isInvalid={!!errors[`guest${index}_lastName`]}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors[`guest${index}_lastName`]}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>

                  {/* Email (Main Guest Only) */}
                  {guest.isMainGuest && (
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label>
                          <FaEnvelope className="me-1" />
                          Email Address *
                        </Form.Label>
                        <Form.Control
                          type="email"
                          placeholder="Enter email address"
                          value={guest.email}
                          onChange={(e) => handleInputChange(index, 'email', e.target.value)}
                          isInvalid={!!errors[`guest${index}_email`]}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors[`guest${index}_email`]}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                  )}

                  {/* Phone (Main Guest Only) */}
                  {guest.isMainGuest && (
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label>
                          <FaPhone className="me-1" />
                          Phone Number *
                        </Form.Label>
                        <Form.Control
                          type="tel"
                          placeholder="Enter phone number"
                          value={guest.phone}
                          onChange={(e) => handleInputChange(index, 'phone', e.target.value)}
                          isInvalid={!!errors[`guest${index}_phone`]}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors[`guest${index}_phone`]}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                  )}

                  {/* Date of Birth */}
                  <Col md={guest.isMainGuest ? 12 : 6}>
                    <Form.Group>
                      <Form.Label>
                        <FaCalendarAlt className="me-1" />
                        Date of Birth *
                      </Form.Label>
                      <div>
                        <DatePicker
                          selected={guest.dateOfBirth}
                          onChange={(date) => handleInputChange(index, 'dateOfBirth', date)}
                          dateFormat="dd/MM/yyyy"
                          className={`form-control ${errors[`guest${index}_dateOfBirth`] ? 'is-invalid' : ''}`}
                          placeholderText="Select date of birth"
                          maxDate={new Date()}
                          showYearDropdown
                          yearDropdownItemNumber={100}
                          scrollableYearDropdown
                        />
                        {errors[`guest${index}_dateOfBirth`] && (
                          <div className="invalid-feedback d-block">
                            {errors[`guest${index}_dateOfBirth`]}
                          </div>
                        )}
                      </div>
                    </Form.Group>
                  </Col>

                  {/* Special Requests */}
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>Special Requests (Optional)</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={2}
                        placeholder="Any special requests or requirements..."
                        value={guest.specialRequests}
                        onChange={(e) => handleInputChange(index, 'specialRequests', e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {index < guests.length - 1 && <hr className="my-4" />}
              </div>
            ))}
          </Card.Body>
        </Card>

        {/* Form Actions */}
        <div className="form-actions d-flex justify-content-between">
          <Button variant="outline-secondary" onClick={onBack}>
            Back to Room Selection
          </Button>
          <Button
            type="submit"
            variant="primary"
            size="lg"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" />
                Processing...
              </>
            ) : (
              'Continue to Payment'
            )}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default HotelGuestForm;
