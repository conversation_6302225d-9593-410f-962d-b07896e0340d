/* General Page Styles */
.page-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-container h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.page-container p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Home Page Specific */
.home-container {
  padding: 0;
}

.hero-section {
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/images/bangladesh-hero.jpg') center/cover;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.featured-section, .testimonials-section {
  padding: 4rem 2rem;
  text-align: center;
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.destination-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.destination-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.testimonial-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
}

.testimonial-card p {
  font-style: italic;
  margin-bottom: 1rem;
}

.customer {
  font-weight: bold;
}
