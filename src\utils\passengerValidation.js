/**
 * Validates a single passenger object.
 * @param {Object} passenger - The passenger object to validate.
 * @param {Array<Object>} allPassengers - Array of all passengers for cross-passenger validation.
 * @returns {Object} An object containing validation errors, keyed by field name.
 */
export const validatePassenger = (passenger, allPassengers) => {
  const errors = {};
  const today = new Date();
  const dobDate = new Date(passenger.dob);

  // Name validations with enhanced rules
  if (!passenger.firstName || !passenger.firstName.trim()) {
    errors.firstName = "First name is required";
  } else if (!/^[A-Za-z\s-']{2,50}$/.test(passenger.firstName.trim())) {
    errors.firstName =
      "First name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes";
  }

  if (!passenger.lastName || !passenger.lastName.trim()) {
    errors.lastName = "Last name is required";
  } else if (!/^[A-Za-z\s-']{2,50}$/.test(passenger.lastName.trim())) {
    errors.lastName =
      "Last name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes";
  }

  // Enhanced passport validation with proper format checks
  if (passenger.type !== "INFANT") {
    if (!passenger.passport || !passenger.passport.trim()) {
      errors.passport = "Passport is required";
    } else if (!/^[A-Z0-9]{6,9}$/i.test(passenger.passport.trim())) {
      errors.passport =
        "Passport number must be 6-9 characters (letters and numbers only)";
    }

    // Passport expiry validation
    if (!passenger.passportExpiry) {
      errors.passportExpiry = "Passport expiry date is required";
    } else {
      const expiryDate = new Date(passenger.passportExpiry);
      const sixMonthsFromNow = new Date();
      sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);

      if (expiryDate <= today) {
        errors.passportExpiry = "Passport has expired";
      } else if (expiryDate <= sixMonthsFromNow) {
        errors.passportExpiry =
          "Passport must be valid for at least 6 months from today";
      }
    }
  } else if (passenger.passport && passenger.passport.trim()) {
    // Optional passport validation for infants if provided
    if (!/^[A-Z0-9]{6,9}$/i.test(passenger.passport.trim())) {
      errors.passport =
        "If provided, passport number must be 6-9 characters (letters and numbers only)";
    }
  }

  // Enhanced date of birth validation
  if (!passenger.dob) {
    errors.dob = "Date of birth is required";
  } else if (isNaN(dobDate.getTime())) {
    errors.dob = "Invalid date format";
  } else if (dobDate > today) {
    errors.dob = "Date of birth cannot be in the future";
  } else {
    const ageInMonths = Math.floor(
      (today - dobDate) / (1000 * 60 * 60 * 24 * 30.44)
    );
    const ageInYears = ageInMonths / 12;

    if (passenger.type === "ADULT") {
      if (ageInYears < 18) {
        errors.dob = "Adults must be 18 years or older";
      } else if (ageInYears > 120) {
        errors.dob = "Please verify the date of birth";
      }
    } else if (passenger.type === "CHILD") {
      if (ageInYears < 2 || ageInYears >= 12) {
        errors.dob = "Children must be between 2 and 11 years old";
      }
    } else if (passenger.type === "INFANT") {
      if (ageInMonths >= 24) {
        errors.dob = "Infants must be under 24 months old";
      }
    }
  }

  // Enhanced contact information validation
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!passenger.email || !passenger.email.trim()) {
    errors.email = "Email is required";
  } else if (!emailRegex.test(passenger.email.trim())) {
    errors.email = "Please enter a valid email address";
  }

  // Phone number validation with international format support
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  if (!passenger.phone || !passenger.phone.trim()) {
    errors.phone = "Phone number is required";
  } else {
    const cleanPhone = passenger.phone.replace(/[\s-]/g, "");
    if (!phoneRegex.test(cleanPhone)) {
      errors.phone =
        "Please enter a valid international phone number (E.164 format)";
    }
  }

  // Gender validation
  if (!passenger.gender) {
    errors.gender = "Gender is required";
  } else if (
    !["MALE", "FEMALE", "OTHER", "PREFER_NOT_TO_SAY"].includes(
      passenger.gender.toUpperCase()
    )
  ) {
    errors.gender = "Invalid gender selection";
  }

  // Enhanced infant-adult association validation
  if (passenger.type === "INFANT" && allPassengers) {
    if (!passenger.associatedAdult) {
      errors.associatedAdult =
        "Infant must be associated with an adult passenger";
    } else {
      const adultPassenger = allPassengers.find(
        (p, idx) => p.type === "ADULT" && idx + 1 === passenger.associatedAdult
      );

      if (!adultPassenger) {
        errors.associatedAdult = "Invalid adult association";
      } else {
        // Validate adult's age in relation to infant
        const adultDob = new Date(adultPassenger.dob);
        const adultAgeInYears =
          (today - adultDob) / (1000 * 60 * 60 * 24 * 365.25);

        if (adultAgeInYears < 18) {
          errors.associatedAdult =
            "Associated adult must be at least 18 years old";
        }

        // Ensure adult isn't associated with multiple infants
        const infantCount = allPassengers.filter(
          (p) =>
            p.type === "INFANT" &&
            p.associatedAdult === passenger.associatedAdult
        ).length;

        if (infantCount > 1) {
          errors.associatedAdult =
            "An adult can only be responsible for one infant";
        }
      }
    }
  }

  // Validate special requirements if provided
  if (passenger.specialNeeds && passenger.specialNeeds.trim().length > 500) {
    errors.specialNeeds =
      "Special needs description cannot exceed 500 characters";
  }

  // Validate meal preferences
  if (
    passenger.mealPreference &&
    ![
      "REGULAR",
      "VEGETARIAN",
      "VEGAN",
      "HALAL",
      "KOSHER",
      "GLUTEN_FREE",
      "DIABETIC",
    ].includes(passenger.mealPreference.toUpperCase())
  ) {
    errors.mealPreference = "Invalid meal preference selection";
  }

  // Validate wheelchair assistance
  if (
    passenger.wheelchair &&
    !["NONE", "WCHR", "WCHS", "WCHC"].includes(
      passenger.wheelchair.toUpperCase()
    )
  ) {
    errors.wheelchair = "Invalid wheelchair assistance option";
  }

  return errors;
};
