.booking-modification {
  padding: 1rem;
}

.modification-options .card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.modification-options .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cursor-pointer {
  cursor: pointer;
}

/* Fade animations for modifications */
.modification-enter {
  opacity: 0;
  transform: translateY(20px);
}

.modification-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.modification-exit {
  opacity: 1;
  transform: translateY(0);
}

.modification-exit-active {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 300ms, transform 300ms;
}

/* Status badges */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
}

.status-badge.confirmed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.modified {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

/* Fee display */
.fee-breakdown {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.fee-breakdown .fee-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #dee2e6;
}

.fee-breakdown .fee-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Confirmation dialog */
.confirmation-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 90%;
  width: 500px;
}

.confirmation-dialog .dialog-header {
  margin-bottom: 1.5rem;
}

.confirmation-dialog .dialog-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modification-options .col-md-4 {
    margin-bottom: 1rem;
  }
  
  .confirmation-dialog {
    width: 95%;
    padding: 1.5rem;
  }
}
