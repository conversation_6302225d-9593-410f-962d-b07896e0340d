/* FlightSearch.css */

/* Main Container Styles */
.flight-booking-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.flight-booking-container h2 {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Form Styles */
.flight-search-form {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-control:focus {
  border-color: #3a7bd5;
  box-shadow: 0 0 0 0.2rem rgba(58, 123, 213, 0.25);
}

/* <PERSON><PERSON> Styles */
.search-btn,
.continue-btn {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.search-btn {
  background-color: #3498db;
  color: white;
}

.search-btn:hover {
  background-color: #2980b9;
}

.search-btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.continue-btn {
  background-color: #2ecc71;
  color: white;
}

.continue-btn:hover {
  background-color: #27ae60;
}

.continue-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

/* Flight Results Section Styles */
.flight-item {
  background-color: white;
  margin-bottom: 15px;
  border-radius: 8px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.flight-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-flight {
  background-color: #e8f4fc;
  border-left: 4px solid #3498db !important;
}

/* Alert Styles */
.alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-info {
  background-color: #e7f5fe;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .flight-item .col-md-2 {
    margin-bottom: 10px;
  }

  .flight-item .text-end {
    text-align: center !important;
  }

  .search-btn,
  .continue-btn {
    width: 100%;
  }
}

/* Section Headings */
.flight-booking-container h3 {
  color: #3498db;
  font-weight: 600;
  margin-top: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
}

/* Button Group Styles */
.btn {
  padding: 8px 15px;
  margin: 5px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #3a7bd5;
  border-color: #3a7bd5;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  width: 100%;
}

.btn-primary:hover {
  background-color: #2c3e50;
  border-color: #2c3e50;
}

.nav-pills .nav-link.active {
  background-color: #3a7bd5;
}

.nav-pills .nav-link {
  border-radius: 5px;
  margin: 0 5px;
  color: #495057;
}

.nav-pills .nav-link:hover {
  background-color: #2c3e50;
  color: white;
}

.passenger-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.passenger-controls button {
  background-color: #3a7bd5;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
}

.passenger-controls button:focus {
  outline: none;
  box-shadow: none;
}

.passenger-controls span {
  margin: 0 10px;
  font-weight: bold;
}

.location-swap {
  background-color: #3a7bd5;
  color: white;
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
}

.location-swap:hover {
  background-color: #2c3e50;
}

/* Passenger Details and Payment Sections */
.passenger-details-section,
.payment-section,
.confirmation-section {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.confirmation-section {
  text-align: center;
}

/* Direct Flight Indicator */
.text-success {
  color: #2ecc71 !important;
  font-weight: 600;
}

/* Price Display */
.fw-bold.text-success.fs-5 {
  font-size: 1.25rem !important;
  color: #27ae60 !important;
}

/* Transit Info */
.small.text-muted {
  font-size: 0.85rem;
  color: #7f8c8d !important;
}

/* Airline Logo Container */
.d-flex.flex-column.align-items-center {
  padding: 10px;
}

/* Modal Styles */
.pricing-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.pricing-modal-content {
  background: white;
  border-radius: 8px;
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Pricing Select Styles */
.pricing-select-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.pricing-header {
  padding: 1.5rem;
  background-color: var(--primary);
  color: white;
}

.flight-card {
  transition: transform 0.3s, box-shadow 0.3s;
  background-color: white;
}

.flight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.flight-direction-badge {
  font-size: 0.85rem;
  font-weight: 600;
}

.price-option {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.price-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.price-option.selected {
  border-color: var(--primary);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.price-option-header {
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.price-option-body {
  padding: 1.5rem;
}

.price-amount {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.price-amount .currency {
  font-size: 1rem;
  color: #666;
}

.price-amount .amount {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary);
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.passenger-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.25rem;
}

/* Make form labels black */
.form-label {
  color: #000000 !important;
}
