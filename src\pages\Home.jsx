import React from "react";
import { Link } from "react-router-dom";
import "../assets/styles/Pages.css";

const Home = () => {
  const featuredDestinations = [
    { id: 1, name: '<PERSON>\'s Bazar', image: '/images/cox-bazar.jpg' },
    { id: 2, name: 'Sundarbans', image: '/images/sundarbans.jpg' },
    { id: 3, name: '<PERSON>ylhe<PERSON>', image: '/images/sylhet.jpg' },
  ];

  return (
    <div className="home-container">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1>Discover Bangladesh with Tripstar</h1>
          <p>Book flights, hotels, and experiences across the country</p>
          <Link to="/flights" className="btn btn-primary btn-lg">
            Book Your Flight Now
          </Link>
        </div>
      </section>

      {/* Featured Destinations */}
      <section className="featured-section">
        <h2>Featured Destinations</h2>
        <div className="destinations-grid">
          {featuredDestinations.map((destination) => (
            <div key={destination.id} className="destination-card">
              <div className="destination-image">
                <img src={destination.image} alt={destination.name} />
              </div>
              <h3>{destination.name}</h3>
            </div>
          ))}
        </div>
      </section>

      {/* Testimonials */}
      <section className="testimonials-section">
        <h2>What Our Customers Say</h2>
        <div className="testimonials-grid">
          <div className="testimonial-card">
            <p>"Tripstar made my vacation planning so easy!"</p>
            <div className="customer">- Rahim, Dhaka</div>
          </div>
          <div className="testimonial-card">
            <p>"Best prices for flights I've found anywhere."</p>
            <div className="customer">- Fatima, Chittagong</div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
