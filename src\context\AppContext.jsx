import React, { createContext, useContext, useReducer, useEffect } from "react";

// Initial state with added session management properties
const initialState = {
  user: null,
  recentSearches: [],
  favorites: [],
  theme: "light",
  currency: "BDT",
  isLoading: false,
  error: null,
  sessionTimeout: 30 * 60 * 1000, // 30 minutes
  sessionWarningTime: 25 * 60 * 1000, // 25 minutes
  sessionRefreshInterval: 15 * 60 * 1000, // 15 minutes
  lastActivity: Date.now(),
  lastSessionRefresh: Date.now(),
  formData: {},
  isSessionActive: true,
  showSessionWarning: false,
  validationState: {
    passengerDetailsValid: false,
    passportDetailsValid: {},
    infantAssociationsValid: true,
    specialRequirementsValid: true,
  },
  errors: {},
};

// Action types
export const ActionTypes = {
  SET_USER: "SET_USER",
  ADD_RECENT_SEARCH: "ADD_RECENT_SEARCH",
  CLEAR_RECENT_SEARCHES: "CLEAR_RECENT_SEARCHES",
  ADD_FAVORITE: "ADD_FAVORITE",
  REMOVE_FAVORITE: "REMOVE_FAVORITE",
  SET_THEME: "SET_THEME",
  SET_CURRENCY: "SET_CURRENCY",
  SET_LOADING: "SET_LOADING",
  SET_ERROR: "SET_ERROR",
  CLEAR_ERROR: "CLEAR_ERROR",
  UPDATE_LAST_ACTIVITY: "UPDATE_LAST_ACTIVITY",
  SET_SESSION_STATUS: "SET_SESSION_STATUS",
  SET_SESSION_WARNING: "SET_SESSION_WARNING",
  REFRESH_SESSION: "REFRESH_SESSION",
  SAVE_FORM_DATA: "SAVE_FORM_DATA",
  CLEAR_FORM_DATA: "CLEAR_FORM_DATA",
  UPDATE_VALIDATION_STATE: "UPDATE_VALIDATION_STATE",
  SET_PASSPORT_VALIDATION: "SET_PASSPORT_VALIDATION",
  CLEAR_ALL_ERRORS: "CLEAR_ALL_ERRORS",
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };

    case ActionTypes.ADD_RECENT_SEARCH:
      // Add to the beginning and limit to 5 recent searches
      const updatedSearches = [
        action.payload,
        ...state.recentSearches.filter(
          (search) => JSON.stringify(search) !== JSON.stringify(action.payload)
        ),
      ].slice(0, 5);

      return {
        ...state,
        recentSearches: updatedSearches,
      };

    case ActionTypes.CLEAR_RECENT_SEARCHES:
      return { ...state, recentSearches: [] };

    case ActionTypes.ADD_FAVORITE:
      return {
        ...state,
        favorites: [...state.favorites, action.payload],
      };

    case ActionTypes.REMOVE_FAVORITE:
      return {
        ...state,
        favorites: state.favorites.filter(
          (fav) => JSON.stringify(fav) !== JSON.stringify(action.payload)
        ),
      };

    case ActionTypes.SET_THEME:
      return { ...state, theme: action.payload };

    case ActionTypes.SET_CURRENCY:
      return { ...state, currency: action.payload };

    case ActionTypes.SET_LOADING:
      return { ...state, isLoading: action.payload };

    case ActionTypes.SET_ERROR:
      if (action.payload.field) {
        // Handle field-specific error
        return {
          ...state,
          errors: {
            ...state.errors,
            [action.payload.field]: action.payload.message,
          },
        };
      } else {
        // Handle general error
        return { ...state, error: action.payload };
      }

    case ActionTypes.CLEAR_ERROR:
      if (action.payload) {
        // Clear specific field error
        const newErrors = { ...state.errors };
        delete newErrors[action.payload];
        return { ...state, errors: newErrors };
      } else {
        // Clear all errors
        return { ...state, error: null, errors: {} };
      }

    case ActionTypes.UPDATE_LAST_ACTIVITY:
      return { ...state, lastActivity: Date.now() };

    case ActionTypes.SET_SESSION_STATUS:
      return { ...state, isSessionActive: action.payload };

    case ActionTypes.SET_SESSION_WARNING:
      return { ...state, showSessionWarning: action.payload };

    case ActionTypes.REFRESH_SESSION:
      return {
        ...state,
        lastSessionRefresh: Date.now(),
        lastActivity: Date.now(),
        isSessionActive: true,
        showSessionWarning: false,
      };

    case ActionTypes.SAVE_FORM_DATA:
      return {
        ...state,
        formData: {
          ...state.formData,
          [action.payload.formId]: action.payload.data,
        },
      };

    case ActionTypes.CLEAR_FORM_DATA:
      const updatedFormData = { ...state.formData };
      delete updatedFormData[action.payload];
      return { ...state, formData: updatedFormData };

    case ActionTypes.UPDATE_VALIDATION_STATE:
      return {
        ...state,
        validationState: {
          ...state.validationState,
          ...action.payload,
        },
      };

    case ActionTypes.SET_PASSPORT_VALIDATION:
      return {
        ...state,
        validationState: {
          ...state.validationState,
          passportDetailsValid: {
            ...state.validationState.passportDetailsValid,
            [action.payload.passengerId]: action.payload.isValid,
          },
        },
      };

    default:
      return state;
  }
};

// Create context
const AppContext = createContext();

// Context provider component
export const AppProvider = ({ children }) => {
  // Load state from localStorage if available
  const loadInitialState = () => {
    try {
      const savedState = localStorage.getItem("appState");
      return savedState
        ? { ...initialState, ...JSON.parse(savedState) }
        : initialState;
    } catch (error) {
      console.error("Error loading state from localStorage:", error);
      return initialState;
    }
  };

  const [state, dispatch] = useReducer(appReducer, loadInitialState());

  // Save state to localStorage when it changes
  useEffect(() => {
    try {
      // Only save specific parts of the state to localStorage
      const stateToSave = {
        recentSearches: state.recentSearches,
        favorites: state.favorites,
        theme: state.theme,
        currency: state.currency,
      };
      localStorage.setItem("appState", JSON.stringify(stateToSave));
    } catch (error) {
      console.error("Error saving state to localStorage:", error);
    }
  }, [state.recentSearches, state.favorites, state.theme, state.currency]);

  // Apply theme to body
  useEffect(() => {
    document.body.dataset.theme = state.theme;
  }, [state.theme]);

  // Session timeout monitoring
  useEffect(() => {
    let timeoutId;
    let refreshIntervalId;

    const checkSession = () => {
      const currentTime = Date.now();
      const timeSinceLastActivity = currentTime - state.lastActivity;

      // Show warning before session expires
      if (
        timeSinceLastActivity >= state.sessionWarningTime &&
        !state.showSessionWarning &&
        state.isSessionActive
      ) {
        dispatch({ type: ActionTypes.SET_SESSION_WARNING, payload: true });
      }

      // Handle session timeout
      if (
        timeSinceLastActivity >= state.sessionTimeout &&
        state.isSessionActive
      ) {
        dispatch({ type: ActionTypes.SET_SESSION_STATUS, payload: false });
        // Save current state before expiring
        const currentPath = window.location.pathname;
        if (currentPath.includes("/booking")) {
          const bookingState = JSON.parse(
            sessionStorage.getItem("bookingState") || "{}"
          );
          sessionStorage.setItem(
            "lastBookingState",
            JSON.stringify(bookingState)
          );
        }
      }
    };

    const refreshSession = async () => {
      try {
        // Add API call to refresh server-side session if needed
        // await refreshServerSession();
        dispatch({ type: ActionTypes.REFRESH_SESSION });
      } catch (error) {
        console.error("Failed to refresh session:", error);
      }
    };

    const handleUserActivity = () => {
      dispatch({ type: ActionTypes.UPDATE_LAST_ACTIVITY });
      if (state.showSessionWarning) {
        dispatch({ type: ActionTypes.SET_SESSION_WARNING, payload: false });
      }
    };

    // Set up periodic session checks and refresh
    timeoutId = setInterval(checkSession, 60000); // Check every minute
    refreshIntervalId = setInterval(
      refreshSession,
      state.sessionRefreshInterval
    );

    // Event listeners for user activity
    const activities = [
      "mousemove",
      "keydown",
      "click",
      "scroll",
      "touchstart",
    ];
    activities.forEach((activity) => {
      window.addEventListener(activity, handleUserActivity);
    });

    return () => {
      clearInterval(timeoutId);
      clearInterval(refreshIntervalId);
      activities.forEach((activity) => {
        window.removeEventListener(activity, handleUserActivity);
      });
    };
  }, [
    state.lastActivity,
    state.sessionTimeout,
    state.sessionWarningTime,
    state.showSessionWarning,
    state.isSessionActive,
  ]);

  // Save session data to localStorage
  useEffect(() => {
    try {
      const sessionData = {
        formData: state.formData,
        lastActivity: state.lastActivity,
        isSessionActive: state.isSessionActive,
      };
      localStorage.setItem("sessionData", JSON.stringify(sessionData));
    } catch (error) {
      console.error("Error saving session data:", error);
    }
  }, [state.formData, state.lastActivity, state.isSessionActive]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
      {!state.isSessionActive && (
        <SessionTimeoutModal
          onReactivate={() =>
            dispatch({ type: ActionTypes.SET_SESSION_STATUS, payload: true })
          }
          onLogout={() => {
            /* implement logout logic */
          }}
        />
      )}
    </AppContext.Provider>
  );
};

// Custom hook for using the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

// Action creators
export const appActions = {
  setUser: (user) => ({ type: ActionTypes.SET_USER, payload: user }),
  addRecentSearch: (search) => ({
    type: ActionTypes.ADD_RECENT_SEARCH,
    payload: search,
  }),
  clearRecentSearches: () => ({ type: ActionTypes.CLEAR_RECENT_SEARCHES }),
  addFavorite: (favorite) => ({
    type: ActionTypes.ADD_FAVORITE,
    payload: favorite,
  }),
  removeFavorite: (favorite) => ({
    type: ActionTypes.REMOVE_FAVORITE,
    payload: favorite,
  }),
  setTheme: (theme) => ({ type: ActionTypes.SET_THEME, payload: theme }),
  setCurrency: (currency) => ({
    type: ActionTypes.SET_CURRENCY,
    payload: currency,
  }),
  setLoading: (isLoading) => ({
    type: ActionTypes.SET_LOADING,
    payload: isLoading,
  }),
  setError: (error) => ({ type: ActionTypes.SET_ERROR, payload: error }),
  clearError: () => ({ type: ActionTypes.CLEAR_ERROR }),
  updateLastActivity: () => ({ type: ActionTypes.UPDATE_LAST_ACTIVITY }),
  setSessionStatus: (status) => ({
    type: ActionTypes.SET_SESSION_STATUS,
    payload: status,
  }),
  setSessionWarning: (warning) => ({
    type: ActionTypes.SET_SESSION_WARNING,
    payload: warning,
  }),
  refreshSession: () => ({ type: ActionTypes.REFRESH_SESSION }),
  saveFormData: (formId, data) => ({
    type: ActionTypes.SAVE_FORM_DATA,
    payload: { formId, data },
  }),
  clearFormData: (formId) => ({
    type: ActionTypes.CLEAR_FORM_DATA,
    payload: formId,
  }),
  updateValidationState: (validationState) => ({
    type: ActionTypes.UPDATE_VALIDATION_STATE,
    payload: validationState,
  }),
  setPassportValidation: (passengerId, isValid) => ({
    type: ActionTypes.SET_PASSPORT_VALIDATION,
    payload: { passengerId, isValid },
  }),
  clearAllErrors: () => ({ type: ActionTypes.CLEAR_ALL_ERRORS }),
};
