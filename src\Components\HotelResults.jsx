import React, { useState } from 'react';
import { Card, Row, Col, Button, Badge, Alert, Spinner } from 'react-bootstrap';
import { FaStar, FaMapMarkerAlt, FaWifi, FaSwimmingPool, FaUtensils, FaSpa, FaD<PERSON><PERSON>ll, FaBed, FaUsers } from 'react-icons/fa';

const HotelResults = ({ hotels, searchParams, onSelectHotel, isLoading }) => {
  const [sortBy, setSortBy] = useState('price'); // price, rating, name
  const [filterBy, setFilterBy] = useState('all'); // all, 3star, 4star, 5star

  // Amenity icons mapping
  const amenityIcons = {
    WIFI: <FaWifi />,
    SWIMMING_POOL: <FaSwimmingPool />,
    RESTAURANT: <FaUtensils />,
    SPA: <FaSpa />,
    FITNESS_CENTER: <FaDumbbell />,
    BUSINESS_CENTER: <FaBed />,
  };

  // Sort hotels
  const sortedHotels = [...(hotels || [])].sort((a, b) => {
    switch (sortBy) {
      case 'price':
        const priceA = parseFloat(a.offers?.[0]?.price?.total || 0);
        const priceB = parseFloat(b.offers?.[0]?.price?.total || 0);
        return priceA - priceB;
      case 'rating':
        const ratingA = parseInt(a.hotel?.rating || 0);
        const ratingB = parseInt(b.hotel?.rating || 0);
        return ratingB - ratingA;
      case 'name':
        return (a.hotel?.name || '').localeCompare(b.hotel?.name || '');
      default:
        return 0;
    }
  });

  // Filter hotels
  const filteredHotels = sortedHotels.filter(hotel => {
    if (filterBy === 'all') return true;
    const rating = parseInt(hotel.hotel?.rating || 0);
    switch (filterBy) {
      case '3star':
        return rating === 3;
      case '4star':
        return rating === 4;
      case '5star':
        return rating === 5;
      default:
        return true;
    }
  });

  // Render star rating
  const renderStars = (rating) => {
    const stars = [];
    const numRating = parseInt(rating || 0);
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <FaStar
          key={i}
          className={i <= numRating ? 'text-warning' : 'text-muted'}
        />
      );
    }
    return stars;
  };

  // Format price
  const formatPrice = (price) => {
    if (!price) return 'Price not available';
    return `৳${parseFloat(price.total).toLocaleString()} ${price.currency}`;
  };

  // Get main amenities to display
  const getMainAmenities = (amenities) => {
    if (!amenities) return [];
    const mainAmenities = ['WIFI', 'SWIMMING_POOL', 'RESTAURANT', 'SPA', 'FITNESS_CENTER'];
    return amenities.filter(amenity => mainAmenities.includes(amenity)).slice(0, 4);
  };

  if (isLoading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">Searching for hotels...</p>
      </div>
    );
  }

  if (!hotels || hotels.length === 0) {
    return (
      <Alert variant="info" className="text-center">
        <h5>No hotels found</h5>
        <p>Try adjusting your search criteria or selecting a different destination.</p>
      </Alert>
    );
  }

  return (
    <div className="hotel-results">
      {/* Search Summary */}
      <div className="search-summary mb-4 p-3 bg-light rounded">
        <Row className="align-items-center">
          <Col md={8}>
            <h5 className="mb-1">
              {hotels.length} hotel{hotels.length !== 1 ? 's' : ''} found
            </h5>
            <p className="mb-0 text-muted">
              {searchParams?.checkInDate} to {searchParams?.checkOutDate} • {searchParams?.adults} adult{searchParams?.adults !== 1 ? 's' : ''} • {searchParams?.roomQuantity} room{searchParams?.roomQuantity !== 1 ? 's' : ''}
            </p>
          </Col>
          <Col md={4}>
            <Row>
              <Col>
                <label className="form-label small">Sort by:</label>
                <select 
                  className="form-select form-select-sm"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <option value="price">Price (Low to High)</option>
                  <option value="rating">Star Rating</option>
                  <option value="name">Hotel Name</option>
                </select>
              </Col>
              <Col>
                <label className="form-label small">Filter:</label>
                <select 
                  className="form-select form-select-sm"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                >
                  <option value="all">All Hotels</option>
                  <option value="3star">3 Star</option>
                  <option value="4star">4 Star</option>
                  <option value="5star">5 Star</option>
                </select>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>

      {/* Hotel List */}
      <div className="hotel-list">
        {filteredHotels.map((hotelOffer, index) => {
          const hotel = hotelOffer.hotel;
          const offer = hotelOffer.offers?.[0];
          const mainAmenities = getMainAmenities(hotel?.amenities);

          return (
            <Card key={index} className="hotel-card mb-4 shadow-sm">
              <Card.Body>
                <Row>
                  {/* Hotel Image Placeholder */}
                  <Col md={3}>
                    <div 
                      className="hotel-image bg-light d-flex align-items-center justify-content-center rounded"
                      style={{ height: '200px', backgroundColor: '#f8f9fa' }}
                    >
                      <FaBed size={40} className="text-muted" />
                    </div>
                  </Col>

                  {/* Hotel Details */}
                  <Col md={6}>
                    <div className="hotel-details">
                      <div className="d-flex align-items-center mb-2">
                        <h5 className="hotel-name mb-0 me-2">{hotel?.name}</h5>
                        <div className="star-rating">
                          {renderStars(hotel?.rating)}
                        </div>
                      </div>

                      <p className="hotel-address text-muted mb-2">
                        <FaMapMarkerAlt className="me-1" />
                        {hotel?.address?.lines?.[0]}, {hotel?.address?.cityName}
                        {hotel?.hotelDistance && (
                          <span className="ms-2">
                            ({hotel.hotelDistance.distance} {hotel.hotelDistance.distanceUnit} from center)
                          </span>
                        )}
                      </p>

                      {/* Room Details */}
                      {offer?.room && (
                        <div className="room-details mb-2">
                          <Badge bg="secondary" className="me-2">
                            {offer.room.typeEstimated?.category?.replace('_', ' ') || 'Room'}
                          </Badge>
                          {offer.room.typeEstimated?.bedType && (
                            <Badge bg="outline-secondary" className="me-2">
                              {offer.room.typeEstimated.bedType} Bed
                            </Badge>
                          )}
                          <Badge bg="outline-secondary">
                            <FaUsers className="me-1" />
                            {offer.guests?.adults || searchParams?.adults} Guest{(offer.guests?.adults || searchParams?.adults) !== 1 ? 's' : ''}
                          </Badge>
                        </div>
                      )}

                      {/* Amenities */}
                      {mainAmenities.length > 0 && (
                        <div className="amenities mb-2">
                          <small className="text-muted">Amenities: </small>
                          {mainAmenities.map((amenity, idx) => (
                            <span key={idx} className="amenity-icon me-2" title={amenity.replace('_', ' ')}>
                              {amenityIcons[amenity] || amenity}
                            </span>
                          ))}
                          {hotel?.amenities?.length > mainAmenities.length && (
                            <small className="text-muted">
                              +{hotel.amenities.length - mainAmenities.length} more
                            </small>
                          )}
                        </div>
                      )}

                      {/* Room Description */}
                      {offer?.room?.description?.text && (
                        <p className="room-description text-muted small mb-0">
                          {offer.room.description.text}
                        </p>
                      )}
                    </div>
                  </Col>

                  {/* Price and Booking */}
                  <Col md={3} className="text-end">
                    <div className="price-booking">
                      {offer?.price && (
                        <div className="price-section mb-3">
                          <div className="price-total">
                            <h4 className="text-primary mb-0">
                              {formatPrice(offer.price)}
                            </h4>
                            <small className="text-muted">
                              Total for {searchParams?.roomQuantity || 1} room{(searchParams?.roomQuantity || 1) !== 1 ? 's' : ''}
                            </small>
                          </div>
                          {offer.price.taxes && offer.price.taxes.length > 0 && (
                            <small className="text-muted d-block">
                              Includes ৳{offer.price.taxes.reduce((sum, tax) => sum + parseFloat(tax.amount), 0).toFixed(2)} taxes
                            </small>
                          )}
                        </div>
                      )}

                      {/* Cancellation Policy */}
                      {offer?.policies?.cancellation && (
                        <div className="cancellation-policy mb-3">
                          <Badge 
                            bg={offer.policies.cancellation.type === 'FREE_CANCELLATION' ? 'success' : 'warning'}
                            className="mb-1"
                          >
                            {offer.policies.cancellation.type === 'FREE_CANCELLATION' ? 'Free Cancellation' : 'Cancellation Fee'}
                          </Badge>
                        </div>
                      )}

                      <Button
                        variant="primary"
                        size="lg"
                        className="w-100"
                        onClick={() => onSelectHotel && onSelectHotel(hotelOffer, offer)}
                      >
                        Select Room
                      </Button>

                      <Button
                        variant="outline-primary"
                        size="sm"
                        className="w-100 mt-2"
                      >
                        View Details
                      </Button>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          );
        })}
      </div>

      {filteredHotels.length === 0 && hotels.length > 0 && (
        <Alert variant="warning" className="text-center">
          <h5>No hotels match your filters</h5>
          <p>Try adjusting the filter criteria to see more results.</p>
        </Alert>
      )}
    </div>
  );
};

export default HotelResults;
