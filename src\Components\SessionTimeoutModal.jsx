import React, { useState, useEffect, useCallback } from "react";
import { useAppContext } from "../context/AppContext";
import { BookingStateManager } from "../services/bookingStateManager";
import "../assets/styles/Booking.css";

const SessionTimeoutModal = ({ onReactivate, onLogout }) => {
  const { state } = useAppContext();
  const [timeLeft, setTimeLeft] = useState(5 * 60); // 5 minutes in seconds
  const [isWarning, setIsWarning] = useState(false);
  const [recoveryAvailable, setRecoveryAvailable] = useState(false);

  useEffect(() => {
    // Check if there's recoverable state
    const savedState = BookingStateManager.getBookingState();
    setRecoveryAvailable(!!savedState);

    setIsWarning(state.showSessionWarning);
    if (state.showSessionWarning) {
      const warningTimeLeft = Math.floor(
        (state.sessionTimeout - (Date.now() - state.lastActivity)) / 1000
      );
      setTimeLeft(warningTimeLeft);
    }
  }, [state.showSessionWarning, state.sessionTimeout, state.lastActivity]);

  useEffect(() => {
    if (isWarning && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [isWarning, timeLeft]);

  const handleRecoveryAttempt = useCallback(async () => {
    const savedState = BookingStateManager.getBookingState();
    if (savedState) {
      onReactivate(savedState);
      setIsWarning(false);
    }
  }, [onReactivate]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleKeepSession = () => {
    onReactivate();
    setIsWarning(false);
  };

  const handleEndSession = () => {
    BookingStateManager.saveLastBookingState(state);
    onLogout();
    setIsWarning(false);
  };

  if (!isWarning && state.isSessionActive) return null;

  return (
    <div className="session-timeout-modal">
      <div className="session-timeout-content">
        <i
          className={`fas fa-clock ${
            timeLeft <= 60 ? "text-danger" : "text-warning"
          } mb-3`}
          style={{ fontSize: "2rem" }}
        ></i>
        <h4>Session Timeout Warning</h4>
        {timeLeft > 0 ? (
          <>
            <p>
              Your session will expire in{" "}
              <strong className={timeLeft <= 60 ? "text-danger" : ""}>
                {formatTime(timeLeft)}
              </strong>
            </p>
            <p className="text-muted small">
              Any unsaved changes may be lost if you don't take action.
            </p>
            <div className="d-grid gap-2">
              <button className="btn btn-primary" onClick={handleKeepSession}>
                <i className="fas fa-sync-alt me-2"></i>
                Keep Session Active
              </button>
              {recoveryAvailable && (
                <p className="text-success small mt-2">
                  <i className="fas fa-info-circle me-1"></i>
                  Your booking progress is safely saved
                </p>
              )}
              <button
                className="btn btn-outline-secondary"
                onClick={handleEndSession}
              >
                End Session
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-danger">Your session has expired.</p>
            {recoveryAvailable ? (
              <>
                <p>Would you like to recover your previous booking progress?</p>
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-success"
                    onClick={handleRecoveryAttempt}
                  >
                    <i className="fas fa-history me-2"></i>
                    Recover Session
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={handleEndSession}
                  >
                    Start New Session
                  </button>
                </div>
              </>
            ) : (
              <div className="d-grid">
                <button
                  className="btn btn-primary"
                  onClick={() => window.location.reload()}
                >
                  <i className="fas fa-redo me-2"></i>
                  Start New Session
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SessionTimeoutModal;
