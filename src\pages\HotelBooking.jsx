import React, { useState } from 'react';
import { Container, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '../context/AppContext';
import HotelGuestForm from '../Components/HotelGuestForm';
import PaymentForm from '../Components/PaymentForm';
import HotelBookingConfirmation from '../Components/HotelBookingConfirmation';
import { createHotelBooking, formatHotelGuests } from '../services/hotelService';
import { processPayment } from '../services/apiService';
import '../assets/styles/Pages.css';

const HotelBooking = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useAppContext();
  
  // Get booking data from state (passed from hotel search/selection)
  const hotelBookingData = state.hotelBookingData || {};
  const { hotelOffer, selectedRooms, searchParams } = hotelBookingData;

  const [currentStep, setCurrentStep] = useState('guest-details'); // guest-details, payment, confirmation
  const [guestDetails, setGuestDetails] = useState(null);
  const [paymentDetails, setPaymentDetails] = useState(null);
  const [bookingConfirmation, setBookingConfirmation] = useState(null);
  const [error, setError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Redirect if no booking data
  if (!hotelOffer || !selectedRooms) {
    return (
      <Container className="py-5">
        <Alert variant="warning" className="text-center">
          <h5>No hotel booking data found</h5>
          <p>Please start a new hotel search to make a booking.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/hotels')}
          >
            Search Hotels
          </button>
        </Alert>
      </Container>
    );
  }

  // Calculate total price
  const calculateTotalPrice = () => {
    return selectedRooms.reduce((total, room) => {
      return total + (parseFloat(room.price?.total || 0) * (room.quantity || 1));
    }, 0);
  };

  // Handle guest details submission
  const handleGuestDetailsSubmit = async (guests) => {
    try {
      setGuestDetails(guests);
      setCurrentStep('payment');
      setError('');
    } catch (err) {
      console.error('Guest details submission failed:', err);
      setError('Failed to save guest details. Please try again.');
    }
  };

  // Handle payment submission
  const handlePaymentSubmit = async (payment) => {
    setIsProcessing(true);
    setError('');

    try {
      const totalAmount = calculateTotalPrice();

      // Process payment
      const paymentResult = await processPayment(payment, totalAmount);
      
      if (paymentResult.success || paymentResult.status === 'COMPLETED') {
        setPaymentDetails({
          ...payment,
          transactionId: paymentResult.transactionId,
          status: paymentResult.status
        });

        // Create hotel booking
        const formattedGuests = formatHotelGuests(guestDetails);
        const bookingResult = await createHotelBooking(
          hotelOffer,
          formattedGuests,
          {
            method: payment.method,
            transactionId: paymentResult.transactionId,
            amount: totalAmount,
            currency: 'BDT'
          }
        );

        setBookingConfirmation(bookingResult);
        setCurrentStep('confirmation');

        // Clear hotel booking data from state
        dispatch({ 
          type: 'SAVE_FORM_DATA', 
          payload: { 
            ...state.formData, 
            hotelBookingData: null 
          } 
        });

        // Save booking to recent bookings (if you have this feature)
        // dispatch({ type: 'ADD_RECENT_BOOKING', payload: bookingResult });

      } else {
        throw new Error('Payment processing failed');
      }

    } catch (err) {
      console.error('Booking process failed:', err);
      setError(err.message || 'Booking failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    switch (currentStep) {
      case 'payment':
        setCurrentStep('guest-details');
        break;
      case 'guest-details':
        navigate('/hotels');
        break;
      default:
        navigate('/hotels');
    }
  };

  // Handle new search from confirmation
  const handleNewSearch = () => {
    navigate('/hotels');
  };

  // Handle view bookings from confirmation
  const handleViewBookings = () => {
    // Navigate to bookings page (you might need to create this)
    navigate('/bookings');
  };

  return (
    <Container className="py-4">
      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Progress Indicator */}
      <div className="booking-progress mb-4">
        <div className="d-flex justify-content-center">
          <div className="progress-steps d-flex align-items-center">
            <div className={`step ${currentStep === 'guest-details' ? 'active' : currentStep !== 'guest-details' ? 'completed' : ''}`}>
              <span className="step-number">1</span>
              <span className="step-label">Guest Details</span>
            </div>
            <div className="step-connector"></div>
            <div className={`step ${currentStep === 'payment' ? 'active' : currentStep === 'confirmation' ? 'completed' : ''}`}>
              <span className="step-number">2</span>
              <span className="step-label">Payment</span>
            </div>
            <div className="step-connector"></div>
            <div className={`step ${currentStep === 'confirmation' ? 'active' : ''}`}>
              <span className="step-number">3</span>
              <span className="step-label">Confirmation</span>
            </div>
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'guest-details' && (
        <HotelGuestForm
          hotelOffer={hotelOffer}
          selectedRooms={selectedRooms}
          searchParams={searchParams}
          onGuestDetailsSubmit={handleGuestDetailsSubmit}
          onBack={handleBack}
        />
      )}

      {currentStep === 'payment' && (
        <PaymentForm
          amount={calculateTotalPrice()}
          currency="BDT"
          onPaymentSubmit={handlePaymentSubmit}
          onBack={handleBack}
          isProcessing={isProcessing}
          bookingType="hotel"
          bookingDetails={{
            hotelName: hotelOffer.hotel?.name,
            checkIn: searchParams?.checkInDate,
            checkOut: searchParams?.checkOutDate,
            guests: guestDetails?.length || 1,
            rooms: selectedRooms?.length || 1
          }}
        />
      )}

      {currentStep === 'confirmation' && bookingConfirmation && (
        <HotelBookingConfirmation
          bookingData={bookingConfirmation}
          hotelOffer={hotelOffer}
          selectedRooms={selectedRooms}
          guests={guestDetails}
          searchParams={searchParams}
          onNewSearch={handleNewSearch}
          onViewBookings={handleViewBookings}
        />
      )}
    </Container>
  );
};

export default HotelBooking;
