import React, { useState, useEffect } from 'react';
import { formatTravelers } from '../services/apiService';

const PassengerModification = ({ passengers, onSubmit, onCancel }) => {
  const [modifiedPassengers, setModifiedPassengers] = useState([]);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    setModifiedPassengers(passengers);
  }, [passengers]);

  const handleInputChange = (index, field, value) => {
    const updated = [...modifiedPassengers];
    updated[index] = {
      ...updated[index],
      [field]: value
    };
    setModifiedPassengers(updated);
    
    // Clear error when field is edited
    if (errors[`${index}-${field}`]) {
      const newErrors = { ...errors };
      delete newErrors[`${index}-${field}`];
      setErrors(newErrors);
    }
  };

  const validateChanges = () => {
    const newErrors = {};
    let isValid = true;

    modifiedPassengers.forEach((passenger, index) => {
      // Validate passport number format
      if (passenger.passport && !/^[A-Z0-9]{6,9}$/i.test(passenger.passport)) {
        newErrors[`${index}-passport`] = 'Invalid passport number format';
        isValid = false;
      }

      // Validate passport expiry
      if (passenger.passportExpiry) {
        const expiryDate = new Date(passenger.passportExpiry);
        if (expiryDate < new Date()) {
          newErrors[`${index}-passportExpiry`] = 'Passport has expired';
          isValid = false;
        }
      }

      // Validate email format
      if (passenger.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(passenger.email)) {
        newErrors[`${index}-email`] = 'Invalid email format';
        isValid = false;
      }

      // Validate phone number
      if (passenger.phone && !/^\+?[\d\s-]{8,}$/.test(passenger.phone)) {
        newErrors[`${index}-phone`] = 'Invalid phone number';
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateChanges()) {
      return;
    }

    // Format travelers data for API
    const formattedTravelers = formatTravelers(modifiedPassengers);
    onSubmit(formattedTravelers);
  };

  return (
    <div className="passenger-modification">
      <h5 className="mb-4">Update Passenger Information</h5>
      
      <form onSubmit={handleSubmit}>
        {modifiedPassengers.map((passenger, index) => (
          <div key={index} className="passenger-form mb-4 p-3 border rounded">
            <h6 className="mb-3">
              Passenger {index + 1}
              <span className="badge bg-secondary ms-2">{passenger.type || 'ADULT'}</span>
            </h6>

            <div className="row g-3">
              {/* First Name */}
              <div className="col-md-6">
                <label className="form-label">First Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={passenger.firstName || ''}
                  onChange={(e) => handleInputChange(index, 'firstName', e.target.value)}
                  required
                />
              </div>

              {/* Last Name */}
              <div className="col-md-6">
                <label className="form-label">Last Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={passenger.lastName || ''}
                  onChange={(e) => handleInputChange(index, 'lastName', e.target.value)}
                  required
                />
              </div>

              {/* Passport Number */}
              <div className="col-md-6">
                <label className="form-label">Passport Number</label>
                <input
                  type="text"
                  className={`form-control ${errors[`${index}-passport`] ? 'is-invalid' : ''}`}
                  value={passenger.passport || ''}
                  onChange={(e) => handleInputChange(index, 'passport', e.target.value.toUpperCase())}
                  required
                />
                {errors[`${index}-passport`] && (
                  <div className="invalid-feedback">{errors[`${index}-passport`]}</div>
                )}
              </div>

              {/* Passport Expiry */}
              <div className="col-md-6">
                <label className="form-label">Passport Expiry Date</label>
                <input
                  type="date"
                  className={`form-control ${errors[`${index}-passportExpiry`] ? 'is-invalid' : ''}`}
                  value={passenger.passportExpiry || ''}
                  onChange={(e) => handleInputChange(index, 'passportExpiry', e.target.value)}
                  required
                />
                {errors[`${index}-passportExpiry`] && (
                  <div className="invalid-feedback">{errors[`${index}-passportExpiry`]}</div>
                )}
              </div>

              {/* Email */}
              <div className="col-md-6">
                <label className="form-label">Email</label>
                <input
                  type="email"
                  className={`form-control ${errors[`${index}-email`] ? 'is-invalid' : ''}`}
                  value={passenger.email || ''}
                  onChange={(e) => handleInputChange(index, 'email', e.target.value)}
                  required
                />
                {errors[`${index}-email`] && (
                  <div className="invalid-feedback">{errors[`${index}-email`]}</div>
                )}
              </div>

              {/* Phone */}
              <div className="col-md-6">
                <label className="form-label">Phone</label>
                <input
                  type="tel"
                  className={`form-control ${errors[`${index}-phone`] ? 'is-invalid' : ''}`}
                  value={passenger.phone || ''}
                  onChange={(e) => handleInputChange(index, 'phone', e.target.value)}
                  required
                />
                {errors[`${index}-phone`] && (
                  <div className="invalid-feedback">{errors[`${index}-phone`]}</div>
                )}
              </div>
            </div>
          </div>
        ))}

        <div className="d-flex gap-2 justify-content-end mt-4">
          <button type="button" className="btn btn-secondary" onClick={onCancel}>
            Cancel
          </button>
          <button type="submit" className="btn btn-primary">
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
};

export default PassengerModification;
