import React from "react";

const Hotels = () => {
  console.log("Hotels component is rendering...");

  return (
    <div
      style={{
        padding: "20px",
        minHeight: "100vh",
        backgroundColor: "#f8f9fa",
      }}
    >
      <h1 style={{ color: "#333", marginBottom: "20px" }}>Hotels Page</h1>
      <p style={{ fontSize: "18px", color: "#666" }}>
        This is a test to see if the Hotels page renders properly.
      </p>
      <div
        style={{
          backgroundColor: "white",
          padding: "20px",
          borderRadius: "8px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          marginTop: "20px",
        }}
      >
        <h3>Hotel Booking System</h3>
        <p>The hotel booking system will be implemented here.</p>
        <button
          style={{
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            padding: "10px 20px",
            borderRadius: "4px",
            cursor: "pointer",
          }}
          onClick={() => alert("Hotels page is working!")}
        >
          Test Button
        </button>
      </div>
    </div>
  );
};

export default Hotels;
