import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "../context/AppContext";
import { BookingStateManager } from "../services/bookingStateManager";

const SessionExpired = () => {
  const navigate = useNavigate();
  const { dispatch } = useAppContext();
  const [lastBookingState, setLastBookingState] = useState(null);

  useEffect(() => {
    const savedState = BookingStateManager.getLastBookingState();
    if (savedState) {
      setLastBookingState(savedState);
    }
  }, []);

  const handleResumeBooking = () => {
    if (lastBookingState) {
      // Reactivate session
      dispatch({ type: "UPDATE_LAST_ACTIVITY" });
      dispatch({ type: "SET_SESSION_STATUS", payload: true });
      dispatch({ type: "REFRESH_SESSION" });

      // Save the recovered state as current booking state
      BookingStateManager.saveBookingState(lastBookingState);
      BookingStateManager.clearAllBookingStates();

      // Navigate back to booking page with saved state
      navigate("/booking", {
        state: {
          selectedFlights: lastBookingState.flights,
          pricingOption: lastBookingState.pricing,
          passengerData: lastBookingState.passengerData,
          seatData: lastBookingState.seatData,
          passengers: lastBookingState.passengerCounts.adults,
          children: lastBookingState.passengerCounts.children,
          infants: lastBookingState.passengerCounts.infants,
        },
      });
    }
  };

  const handleStartNew = () => {
    // Clear any saved booking state
    BookingStateManager.clearAllBookingStates();

    // Reactivate session
    dispatch({ type: "UPDATE_LAST_ACTIVITY" });
    dispatch({ type: "SET_SESSION_STATUS", payload: true });
    dispatch({ type: "REFRESH_SESSION" });

    // Navigate to home page
    navigate("/");
  };

  return (
    <div className="container my-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card shadow">
            <div className="card-body text-center p-5">
              <i
                className="fas fa-clock text-warning mb-4"
                style={{ fontSize: "3rem" }}
              ></i>
              <h2 className="mb-4">Session Expired</h2>
              <p className="text-muted mb-4">
                Your session has expired due to inactivity.
                {lastBookingState && " Would you like to resume your booking?"}
              </p>

              {lastBookingState ? (
                <div className="d-grid gap-3">
                  <button
                    className="btn btn-primary"
                    onClick={handleResumeBooking}
                  >
                    <i className="fas fa-redo me-2"></i>
                    Resume Booking
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={handleStartNew}
                  >
                    <i className="fas fa-home me-2"></i>
                    Start New Search
                  </button>
                </div>
              ) : (
                <div className="d-grid">
                  <button className="btn btn-primary" onClick={handleStartNew}>
                    <i className="fas fa-home me-2"></i>
                    Return to Home
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionExpired;
