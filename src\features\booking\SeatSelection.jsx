import React, { useState, useEffect } from "react";
import "../../assets/styles/Booking.css";
import "../../assets/styles/SeatMap.css";

/**
 * Component for seat selection during the booking process
 */
const SeatSelection = ({ selectedFlights, passengers, pricingOption, onComplete }) => {
  // State for selected seats
  const [selectedSeats, setSelectedSeats] = useState({
    outbound: {},
    return: selectedFlights.return ? {} : null,
  });

  // State for active passenger
  const [activePassenger, setActivePassenger] = useState(passengers[0]?.id || null);

  // State for seat maps
  const [seatMaps, setSeatMaps] = useState({
    outbound: generateSeatMap(selectedFlights.outbound),
    return: selectedFlights.return ? generateSeatMap(selectedFlights.return) : null,
  });

  // State for validation errors
  const [errors, setErrors] = useState([]);

  // Effect to clear errors after 5 seconds
  useEffect(() => {
    if (errors.length > 0) {
      const timer = setTimeout(() => {
        setErrors([]);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errors]);

  // State for tracking infants and their associated adults
  const [infantAssociations, setInfantAssociations] = useState({});

  /**
   * Generate a seat map for a flight
   */
  function generateSeatMap(flight) {
    const rows = 30;
    const seatsPerRow = 6;
    const seatMap = [];

    for (let row = 1; row <= rows; row++) {
      const seatRow = [];
      for (let seat = 0; seat < seatsPerRow; seat++) {
        const seatLetter = String.fromCharCode(65 + seat);
        const seatNumber = `${row}${seatLetter}`;
        
        // Determine seat type and availability based on position
        const isExitRow = row >= 12 && row <= 14;
        const isBassinet = row === 1 && (seatLetter === 'C' || seatLetter === 'D');
        const isPremium = row <= 5;
        const isWindow = seat === 0 || seat === 5;
        const isMiddle = seat === 1 || seat === 4;
        const isAisle = seat === 2 || seat === 3;
        
        seatRow.push({
          id: seatNumber,
          number: seatNumber,
          // Only mark emergency exit rows as unavailable for now
          available: !isExitRow, 
          price: calculateSeatPrice(row, seat, pricingOption),
          type: isExitRow ? "EXIT" : 
                isPremium ? "PREMIUM" : 
                isBassinet ? "BASSINET" : "STANDARD",
          isWindow,
          isMiddle,
          isAisle,
          isBassinet,
          isExitRow
        });
      }
      seatMap.push(seatRow);
    }

    return seatMap;
  }

  /**
   * Calculate price for a seat based on its location and pricing option
   */
  function calculateSeatPrice(row, seat, pricingOption) {
    let basePrice = 0;
    
    // Front rows (1-5) are more expensive
    if (row <= 5) basePrice = 1000;
    // Exit rows (12-14) are more expensive
    else if (row >= 12 && row <= 14) basePrice = 800;
    // Regular seats
    else basePrice = 500;

    // Window seats cost more
    if (seat === 0 || seat === 5) basePrice += 200;
    
    // Apply pricing option multiplier
    if (pricingOption === "flex") basePrice *= 0.9; // 10% discount for flex
    else if (pricingOption === "premium") basePrice *= 0.8; // 20% discount for premium

    return basePrice;
  }

  /**
   * Get the type of seat based on its location
   */
  function getSeatType(row, seat) {
    if (row <= 5) return "PREMIUM";
    if (row >= 12 && row <= 14) return "EXIT";
    return "STANDARD";
  }

  /**
   * Validate seat selection based on passenger type and seat characteristics
   */  const validateSeatSelection = (flightType, seat, passenger) => {
    if (!seat?.available) {
      return {
        valid: false,
        message: "This seat is not available for selection"
      };
    }

    // Handle infant passenger restrictions
    if (passenger?.type === "INFANT") {
      if (!seat.isAisle && !seat.isWindow) {
        return {
          valid: false,
          message: "Infants must be seated in aisle or window seats"
        };
      }

      const associatedAdult = passengers.find(p => p.id === passenger.associatedAdult);
      if (associatedAdult) {
        const adultSeat = getSelectedSeatForPassenger(flightType, associatedAdult.id);
        if (adultSeat && !isAdjacentSeat(seat.number, adultSeat)) {
          return {
            valid: false,
            message: "Infants must be seated next to their accompanying adult"
          };
        }
      }
    }

    // Handle bassinet seat restrictions
    if (seat.isBassinet) {
      if (passenger.type !== "ADULT" || !hasInfantPassenger(passenger.id)) {
        return {
          valid: false,
          message: "Bassinet seats are reserved for adults traveling with infants"
        };
      }
    }

    // Handle exit row restrictions
    if (seat.isExitRow) {
      if (passenger.type === "INFANT" || hasInfantPassenger(passenger.id)) {
        return {
          valid: false,
          message: "Exit row seats cannot be assigned to passengers with infants"
        };
      }

      if (passenger.specialAssistance) {
        return {
          valid: false,
          message: "Exit row seats cannot be assigned to passengers requiring special assistance"
        };
      }

      if (passenger.type === "CHILD" || passenger.age < 15) {
        return {
          valid: false,
          message: "Exit row seats can only be assigned to passengers aged 15 and above"
        };
      }
    }

    return { valid: true };
  };  // Handle seat selection
  const handleSeatSelect = (flightType, seatId) => {
    if (!flightType || !seatId) return;

    const seat = seatMaps[flightType].flat().find(s => s.id === seatId || s.number === seatId);
    if (!seat) {
      console.error(`Seat ${seatId} not found in ${flightType} flight`);
      return;
    }

    // Get the active passenger
    const passenger = passengers.find(p => p.id === activePassenger);
    if (!passenger) {
      setErrors(prev => [...prev, "Please select a passenger first"]);
      return;
    }

    // Check if this seat is already selected by this passenger (deselection case)
    const currentPassengerSeat = getSelectedSeatForPassenger(flightType, passenger.id);
    if (currentPassengerSeat === seatId) {
      // Deselect the seat
      const updatedSeats = { ...selectedSeats[flightType] };
      delete updatedSeats[seatId];
      
      setSelectedSeats(prev => ({
        ...prev,
        [flightType]: updatedSeats
      }));
      return;
    }

    // Check if seat is already taken by another passenger
    const currentSeatPassenger = getPassengerForSeat(flightType, seatId);
    if (currentSeatPassenger && currentSeatPassenger.id !== passenger.id) {
      setErrors(prev => [...prev, `Seat ${seatId} is already assigned to ${currentSeatPassenger.firstName}`]);
      return;
    }

    const validation = validateSeatSelection(flightType, seat, passenger);
    if (!validation.valid) {
      setErrors(prev => [...prev, validation.message]);
      return;
    }

    // Remove any existing seat assignment for this passenger
    const updatedSeats = { ...selectedSeats[flightType] };
    Object.entries(updatedSeats).forEach(([seat, id]) => {
      if (id === passenger.id) {
        delete updatedSeats[seat];
      }
    });

    // Assign the new seat
    setSelectedSeats(prev => ({
      ...prev,
      [flightType]: {
        ...updatedSeats,
        [seatId]: passenger.id
      }
    }));
  };

  /**
   * Validate seat selection
   */
  const validateSeats = () => {
    const validationErrors = [];

    // Transform selected seats into a more usable format
    const outboundSelections = {};
    const returnSelections = {};

    // Map seat numbers to passenger IDs
    Object.entries(selectedSeats.outbound || {}).forEach(([seatNumber, passengerId]) => {
      outboundSelections[passengerId] = seatNumber;
    });

    if (selectedFlights.return) {
      Object.entries(selectedSeats.return || {}).forEach(([seatNumber, passengerId]) => {
        returnSelections[passengerId] = seatNumber;
      });
    }

    // Validate seat assignments for each passenger
    passengers.forEach(passenger => {
      // Check outbound flight
      const outboundSeat = outboundSelections[passenger.id];
      if (!outboundSeat) {
        validationErrors.push(`${passenger.firstName} needs an outbound seat`);
      }

      // Check return flight if applicable
      if (selectedFlights.return) {
        const returnSeat = returnSelections[passenger.id];
        if (!returnSeat) {
          validationErrors.push(`${passenger.firstName} needs a return seat`);
        }
      }

      // Special validation for infants
      if (passenger.type === "INFANT") {
        const associatedAdult = passengers.find(p => p.id === passenger.associatedAdult);
        if (associatedAdult) {
          if (outboundSeat) {
            const adultOutboundSeat = outboundSelections[associatedAdult.id];
            if (!isAdjacentSeat(outboundSeat, adultOutboundSeat)) {
              validationErrors.push(
                `${passenger.firstName} must be seated next to accompanying adult on outbound flight`
              );
            }
          }

          if (selectedFlights.return) {
            const returnSeat = returnSelections[passenger.id];
            const adultReturnSeat = returnSelections[associatedAdult.id];
            if (returnSeat && !isAdjacentSeat(returnSeat, adultReturnSeat)) {
              validationErrors.push(
                `${passenger.firstName} must be seated next to accompanying adult on return flight`
              );
            }
          }
        }
      }
    });

    setErrors(validationErrors);
    return validationErrors.length === 0;
  };

  /**
   * Check if two seats are adjacent
   */
  const isAdjacentSeat = (seat1, seat2) => {
    if (!seat1 || !seat2) return false;
    
    const matches1 = seat1.match(/(\d+)([A-F])/);
    const matches2 = seat2.match(/(\d+)([A-F])/);
    
    if (!matches1 || !matches2) return false;
    
    const row1 = parseInt(matches1[1]);
    const row2 = parseInt(matches2[1]);
    const letter1 = matches1[2];
    const letter2 = matches2[2];

    if (row1 !== row2) return false;

    // Convert letters to numbers (A=0, B=1, etc)
    const seat1Num = letter1.charCodeAt(0) - 65;
    const seat2Num = letter2.charCodeAt(0) - 65;

    // Check if seats are adjacent within the same row
    // Consider aisle between seats C (2) and D (3)
    if (Math.abs(seat1Num - seat2Num) === 1) {
      return true;
    }

    // Check if seats are adjacent across the aisle (C-D)
    if ((seat1Num === 2 && seat2Num === 3) || (seat1Num === 3 && seat2Num === 2)) {
      return true;
    }

    return false;
  };  /**
   * Handle form submission
   */
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateSeats()) {
      // Format seat assignments for easier use in next steps
      const seatData = {
        seatSelections: selectedSeats,
        totalSeatCost: calculateTotalSeatCost(),
        assignments: {
          outbound: Object.entries(selectedSeats.outbound || {}).map(([seat, passengerId]) => ({
            seat,
            passengerId,
            passenger: passengers.find(p => p.id === passengerId)?.firstName || ''
          })),
          return: selectedFlights.return 
            ? Object.entries(selectedSeats.return || {}).map(([seat, passengerId]) => ({
                seat,
                passengerId,
                passenger: passengers.find(p => p.id === passengerId)?.firstName || ''
              }))
            : []
        }
      };
      
      onComplete(seatData);
    } else {
      // Scroll to error message if present
      const errorElement = document.querySelector('.alert-danger');
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  /**
   * Calculate total cost of selected seats
   */
  const calculateTotalSeatCost = () => {
    let total = 0;

    // Add outbound seat costs
    Object.entries(selectedSeats.outbound || {}).forEach(([seatNumber, _]) => {
      const [row, seat] = getSeatRowAndNumber(seatNumber);
      if (row > 0) {
        total += calculateSeatPrice(row, seat, pricingOption);
      }
    });

    // Add return seat costs if applicable
    if (selectedFlights.return && selectedSeats.return) {
      Object.entries(selectedSeats.return).forEach(([seatNumber, _]) => {
        const [row, seat] = getSeatRowAndNumber(seatNumber);
        if (row > 0) {
          total += calculateSeatPrice(row, seat, pricingOption);
        }
      });
    }

    return total;
  };

  /**
   * Extract row and seat number from seat ID
   */
  const getSeatRowAndNumber = (seatId) => {
    if (!seatId) return [0, 0];
    const matches = seatId.match(/(\d+)([A-F])/);
    if (!matches) return [0, 0];
    const row = parseInt(matches[1]);
    const letter = matches[2];
    const seat = letter.charCodeAt(0) - 65;
    return [row, seat];
  };

  const isCurrentPassengerSeat = (flightType, seatNumber, passengerId) => {
    return selectedSeats[flightType]?.[seatNumber] === passengerId;
  };

  // Get passenger details for a seat
  const getPassengerForSeat = (flightType, seatNumber) => {
    const passengerId = selectedSeats[flightType]?.[seatNumber];
    if (!passengerId) return null;
    return passengers.find(p => p.id === passengerId);
  };

  // Get tooltip text for a seat
  const getSeatTooltip = (seat, flightType) => {
    let tooltip = `Seat ${seat.number}`;
    if (seat.type !== "STANDARD") {
      tooltip += ` - ${seat.type}`;
    }
    tooltip += ` - $${seat.price}`;

    const passenger = getPassengerForSeat(flightType, seat.number);
    if (passenger) {
      tooltip += `\nSelected by ${passenger.firstName} ${passenger.lastName}`;
    }

    return tooltip;
  };

  const getSeatClasses = (seat, flightType) => {
    if (!seat) return '';
    
    const classes = ['seat'];
    if (!seat.available) {
      classes.push('unavailable');
    }
    
    const passenger = getPassengerForSeat(flightType, seat.number);
    const isSelectedByActive = passenger?.id === activePassenger;
    
    if (passenger) {
      classes.push('selected');
      if (isSelectedByActive) {
        classes.push('selected-active');
      }
    }
    
    if (seat.type === 'EXIT') classes.push('exit-row');
    if (seat.type === 'PREMIUM') classes.push('premium');
    if (seat.type === 'BASSINET') classes.push('bassinet');
    if (seat.isWindow) classes.push('window');
    if (seat.isAisle) classes.push('aisle');
    
    return classes.join(' ');
  };
  const renderSeatOption = (seat, flightType, passengerId) => {
    if (!seat || !seat.available) {
      return null;
    }

    // Show seat if it's either unselected or selected by this passenger
    const currentPassenger = getPassengerForSeat(flightType, seat.number);
    if (currentPassenger && currentPassenger.id !== passengerId) {
      return null;
    }

    // Create descriptive label for the seat
    let seatLabel = `${seat.number}`;
    
    // Add seat type if not standard
    if (seat.type !== "STANDARD") {
      seatLabel += ` (${seat.type})`;
    }

    // Add position info
    if (seat.isWindow) {
      seatLabel += " - Window";
    } else if (seat.isAisle) {
      seatLabel += " - Aisle";
    } else if (seat.isMiddle) {
      seatLabel += " - Middle";
    }

    // Add price
    seatLabel += ` - $${seat.price}`;

    return (
      <option key={seat.number} value={seat.number}>
        {seatLabel}
      </option>
    );
  };

  const renderSeat = (seat, flightType) => {
    const seatClasses = getSeatClasses(seat, flightType);
    const passenger = getPassengerForSeat(flightType, seat.number);
    const tooltip = getSeatTooltip(seat, flightType);

    return (
      <button
        key={seat.number}
        className={seatClasses}
        onClick={() => handleSeatSelect(flightType, seat.number)}
        disabled={!seat.available}
        title={tooltip}
        aria-label={tooltip}
      >
        <div className="seat-label">{seat.number}</div>
        {passenger && (
          <div className="seat-passenger">
            {passenger.firstName.charAt(0)}
          </div>
        )}
      </button>
    );
  };

  const getSelectedSeatForPassenger = (flightType, passengerId) => {
    const seatNumber = Object.entries(selectedSeats[flightType] || {})
      .find(([_, id]) => id === passengerId)?.[0];
    return seatNumber || "";
  };

  /**
   * Check if a passenger is associated with an infant
   */
  const hasInfantPassenger = (passengerId) => {
    const passenger = passengers.find(p => p.id === passengerId);
    return passenger && passenger.type === "ADULT" && 
      passengers.some(p => p.type === "INFANT" && p.associatedAdult === passenger.id);
  };

  /**
   * Check if a seat is suitable for an infant
   */
  const hasInfantRestriction = (seat) => {
    // Infants cannot sit in exit rows
    if (seat.type === "EXIT") return true;
    
    // Extract row number from seat
    const row = parseInt(seat.number.match(/\d+/)[0]);
    
    // Infants should not sit in the first row (bulkhead)
    if (row === 1) return true;

    return false;
  };

  /**
   * Check if a seat can be selected for an infant based on its location
   */
  const canSelectForInfant = (seat) => {
    if (hasInfantRestriction(seat)) return false;
    
    // Find if any adjacent seats are assigned to adults with infants
    const [row, seatLetter] = getSeatRowAndNumber(seat.number);
    const adjacentSeats = findAdjacentSeats(row, seatLetter);
    
    return adjacentSeats.some(adjacentSeat => {
      const assignedPassengerId = selectedSeats[flightType]?.[adjacentSeat];
      return hasInfantPassenger(assignedPassengerId);
    });
  };

  /**
   * Find all adjacent seats for a given seat position
   */
  const findAdjacentSeats = (row, seatNum) => {
    const seatLetter = String.fromCharCode(65 + seatNum);
    const adjacentSeats = [];
    
    // Check seat to the left
    if (seatNum > 0) {
      adjacentSeats.push(`${row}${String.fromCharCode(64 + seatNum)}`);
    }
    
    // Check seat to the right
    if (seatNum < 5) {
      adjacentSeats.push(`${row}${String.fromCharCode(66 + seatNum)}`);
    }
    
    // Check across the aisle
    if (seatNum === 2) { // Seat C
      adjacentSeats.push(`${row}D`);
    } else if (seatNum === 3) { // Seat D
      adjacentSeats.push(`${row}C`);
    }
    
    return adjacentSeats;
  };

  // Clear errors after 5 seconds
  useEffect(() => {
    if (errors.length > 0) {
      const timer = setTimeout(() => {
        setErrors([]);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errors]);

  // Show currently selected passenger's seat
  const getPassengerSeatDisplay = (flightType, passengerId) => {
    const seatNumber = getSelectedSeatForPassenger(flightType, passengerId);
    if (!seatNumber) return "";
    
    const seat = seatMaps[flightType]?.flat().find(s => s.number === seatNumber);
    if (!seat) return seatNumber;
    
    return `${seatNumber} (${seat.type})`;
  };

  return (
    <div className="seat-selection-container">
      <h3>Select Your Seats</h3>
      <p className="text-muted mb-4">
        Choose your preferred seats for each flight. Additional charges may apply.
      </p>

      <div className="alert alert-info mb-4">
        <strong>Currently selecting for: </strong>
        {passengers.find(p => p.id === activePassenger)?.firstName || 'Please select a passenger'}
      </div>

      {errors.length > 0 && (
        <div className="alert alert-danger">
          <ul className="mb-0">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Outbound Flight Seat Map */}
        <div className="card mb-4">
          <div className="card-header bg-primary text-white">
            <h5 className="mb-0">
              <i className="fas fa-plane-departure me-2"></i>
              Outbound Flight Seats
            </h5>
          </div>
          <div className="card-body">
            <div className="seat-map">
              {seatMaps.outbound.map((row, rowIndex) => (
                <div key={rowIndex} className="seat-row">
                  {row.map((seat, seatIndex) => (
                    renderSeat(seat, "outbound")
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Return Flight Seat Map (if applicable) */}
        {selectedFlights.return && (
          <div className="card mb-4">
            <div className="card-header bg-secondary text-white">
              <h5 className="mb-0">
                <i className="fas fa-plane-arrival me-2"></i>
                Return Flight Seats
              </h5>
            </div>
            <div className="card-body">
              <div className="seat-map">
                {seatMaps.return.map((row, rowIndex) => (
                  <div key={rowIndex} className="seat-row">
                    {row.map((seat, seatIndex) => (
                      renderSeat(seat, "return")
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Passenger Seat Assignment */}
        <div className="card mb-4">
          <div className="card-header">
            <h5 className="mb-0">Passenger Seat Assignment</h5>
          </div>
          <div className="card-body">
            {passengers.map((passenger) => (
              <div key={passenger.id} className="passenger-seat-row mb-3">
                <h6>
                  {passenger.firstName} {passenger.lastName}
                  <span className={`badge ms-2 ${
                    passenger.type === "ADULT" ? "bg-primary" :
                    passenger.type === "CHILD" ? "bg-info" :
                    "bg-warning text-dark"
                  }`}>
                    {passenger.type}
                  </span>
                </h6>
                <div className="row">
                  {/* Outbound flight seat selection */}
                  <div className="col-md-6">
                    <select
                      className="form-select"                      value={getSelectedSeatForPassenger("outbound", passenger.id)}
                      onChange={(e) => {
                        setActivePassenger(passenger.id);
                        handleSeatSelect("outbound", e.target.value);
                      }}
                      required
                    >
                      <option value="">Select Outbound Seat</option>
                      {seatMaps.outbound?.flat().map(seat => 
                        renderSeatOption(seat, "outbound", passenger.id)
                      )}
                    </select>
                  </div>

                  {/* Return flight seat selection */}
                  {selectedFlights.return && (
                    <div className="col-md-6">
                      <select
                        className="form-select"                      value={getSelectedSeatForPassenger("return", passenger.id)}
                      onChange={(e) => {
                        setActivePassenger(passenger.id);
                        handleSeatSelect("return", e.target.value);
                      }}
                      required
                    >
                      <option value="">Select Return Seat</option>
                      {seatMaps.return?.flat().map(seat => 
                        renderSeatOption(seat, "return", passenger.id)
                      )}
                      </select>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="card mb-4">
          <div className="card-body">
            <h6>Seat Legend</h6>
            <div className="d-flex flex-wrap gap-3">
              <div>
                <span className="seat-btn standard me-1"></span>
                Standard Seat
              </div>
              <div>
                <span className="seat-btn premium me-1"></span>
                Premium Seat
              </div>
              <div>
                <span className="seat-btn bassinet me-1"></span>
                Bassinet Seat
              </div>
              <div>
                <span className="seat-btn exit me-1"></span>
                Exit Row
              </div>
              <div>
                <span className="seat-btn selected me-1"></span>
                Selected
              </div>
              <div>
                <span className="seat-btn unavailable me-1"></span>
                Unavailable
              </div>
            </div>
            <div className="mt-3">
              <small className="text-muted">
                <ul className="mb-0">
                  <li>Premium seats (rows 1-5) have extra legroom</li>
                  <li>Bassinet seats are only available for adults traveling with infants</li>
                  <li>Exit row seats require passengers to be at least 15 years old</li>
                  <li>Infants must be seated next to their accompanying adult</li>
                </ul>
              </small>
            </div>
          </div>
        </div>

        {/* Total Cost */}
        <div className="card mb-4">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Total Seat Cost:</h5>
              <h5 className="mb-0">${calculateTotalSeatCost()}</h5>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="d-grid gap-2">
          <button type="submit" className="btn btn-primary btn-lg">
            <i className="fas fa-credit-card me-2"></i>
            Continue to Payment
          </button>
        </div>
      </form>
    </div>
  );
};

export default SeatSelection;
