import React, { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAppContext } from "../context/AppContext";
import { BookingStateManager } from "../services/bookingStateManager";
import PassengerDetails from "../features/booking/PassengerDetails";
import SeatSelection from "../features/booking/SeatSelection";
import Payment from "../features/payment/Payment";
import SessionTimeoutModal from "../Components/SessionTimeoutModal";
import { createBooking, formatTravelers } from "../services/apiService";
import { formatAirportDisplay } from "../utils/airportUtils";
import { PriceCalculationService } from "../services/priceCalculationService";
import "../assets/styles/Booking.css";

const INACTIVITY_WARNING_TIME = 25 * 60 * 1000; // Show warning 5 minutes before timeout

/**
 * BookingPage component that orchestrates the entire booking flow
 */
const BookingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { state: appState, dispatch } = useAppContext();

  const [currentStep, setCurrentStep] = useState(() => {
    // Try to recover the last active step from session storage
    const savedStep = sessionStorage.getItem("bookingStep");
    return savedStep || "passenger-details";
  });

  const [showTimeoutWarning, setShowTimeoutWarning] = useState(false);
  const [passengerData, setPassengerData] = useState(null);
  const [seatData, setSeatData] = useState(null);
  const [bookingData, setBookingData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get data from location state
  const {
    selectedFlights = { outbound: null, return: null }, // Provide default structure
    pricingOption,
    passengers,
    children = 0,
    infants = 0,
  } = location.state || {};

  useEffect(() => {
    // Validate required data
    if (!selectedFlights?.outbound) {
      setError(
        "No flight information provided. Please start your booking again."
      );
    }
  }, [selectedFlights]);

  // Replace calculateTotalPrice with new service
  const calculateTotalPrice = () => {
    if (!selectedFlights) return 0;

    return PriceCalculationService.calculateTotalPrice({
      flights: selectedFlights,
      passengers: {
        adult: passengers,
        child: children,
        infant: infants,
      },
      pricingOption: pricingOption,
      seatSelections: seatData?.seatSelections,
    }).total;
  };

  // Handle passenger details completion
  const handlePassengerDetailsComplete = (data) => {
    // Add IDs to passenger data for seat mapping and ensure they are strings
    const passengersWithIds = data.map((passenger, index) => ({
      ...passenger,
      id: `passenger-${index + 1}`,
      type: passenger.type.toUpperCase(), // Ensure type is uppercase
    }));
    console.log("Setting passenger data:", passengersWithIds);
    setPassengerData(passengersWithIds);
    // Move to the seat selection step
    setCurrentStep("seat-selection");
  };

  // Handle proceeding to seat selection
  const handleProceedToSeatSelection = () => {
    setCurrentStep("seat-selection");
  };

  // Handle skipping seat selection
  const handleSkipSeatSelection = () => {
    setSeatData(null); // Clear any previous seat data
    setCurrentStep("payment");
  };

  // Handle seat selection completion
  const handleSeatSelectionComplete = (data) => {
    setSeatData(data);
    const priceDetails = PriceCalculationService.calculateTotalPrice({
      flights: selectedFlights,
      passengers: {
        adult: passengers,
        child: children,
        infant: infants,
      },
      pricingOption: pricingOption,
      seatSelections: data.seatSelections,
    });

    // Store the detailed price breakdown
    setBookingData((prev) => ({
      ...prev,
      priceDetails,
    }));

    setCurrentStep("payment");
  };

  // Handle payment completion
  const handlePaymentComplete = async (transactionId) => {
    setLoading(true);
    setError(null);

    try {
      console.log("Payment completed with transaction ID:", transactionId);

      // Format flight offers
      const flightOffers = selectedFlights.return
        ? [selectedFlights.outbound, selectedFlights.return]
        : [selectedFlights.outbound];

      // Add seat selections to flight offers if they exist
      if (seatData?.seatSelections) {
        flightOffers.forEach((offer, index) => {
          const flightType = index === 0 ? "outbound" : "return";
          offer.seatSelections = seatData.seatSelections[flightType] || {};
        });
      }

      // Format travelers data
      const travelers = formatTravelers(passengerData);

      // Create booking
      console.log("Attempting to create booking with flight offers:", flightOffers);
      console.log("Attempting to create booking with travelers:", travelers);
      const response = await createBooking(flightOffers, travelers);
      
      console.log("Received booking response:", response);

      if (!response || !response.pnr) {
        throw new Error("No PNR received in booking response");
      }

      // Store the booking response
      setBookingData(response);

      // Navigate to confirmation page with all necessary data
      navigate("/booking-confirm", {
        state: {
          bookingData: response,
          selectedFlights,
          passengerData,
          seatData,
          totalAmount: calculateTotalPrice(),
          pricingOption,
          passengers,
          children,
          infants,
        },
      });
    } catch (err) {
      console.error("Booking failed:", err);
      setError(
        err.message === "No PNR received in booking response"
          ? "Failed to generate booking reference. Please contact support."
          : "Failed to create booking. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  // Render error message if data is missing
  if (error) {
    return (
      <div className="container p-4">
        <div className="alert alert-danger">{error}</div>
        <button className="btn btn-primary mt-3" onClick={() => navigate("/")}>
          Start New Search
        </button>
      </div>
    );
  }

  // Monitor session timeout
  useEffect(() => {
    const checkSessionTimeout = () => {
      const timeSinceLastActivity = Date.now() - appState.lastActivity;

      if (timeSinceLastActivity >= INACTIVITY_WARNING_TIME) {
        setShowTimeoutWarning(true);
      }
    };

    const intervalId = setInterval(checkSessionTimeout, 60000); // Check every minute

    return () => clearInterval(intervalId);
  }, [appState.lastActivity]);

  // Auto-save effect that runs every 30 seconds
  useEffect(() => {
    if (!error && (passengerData || seatData)) {
      const autoSaveInterval = setInterval(() => {
        BookingStateManager.autoSaveBookingState({
          step: currentStep,
          flights: selectedFlights,
          pricing: pricingOption,
          passengerData,
          seatData,
          passengerCounts: {
            adults: passengers,
            children,
            infants,
          },
          lastActivity: Date.now(),
        });
      }, 30 * 1000); // 30 seconds

      return () => clearInterval(autoSaveInterval);
    }
  }, [currentStep, passengerData, seatData, selectedFlights, pricingOption]);

  // Handle session reactivation with state recovery
  const handleSessionReactivate = useCallback((recoveredState) => {
    if (recoveredState) {
      setCurrentStep(recoveredState.step || "passenger-details");
      setPassengerData(recoveredState.passengerData || null);
      setSeatData(recoveredState.seatData || null);
      dispatch({ type: "REACTIVATE_SESSION" });
    } else {
      dispatch({ type: "REACTIVATE_SESSION" });
    }
  }, [dispatch]);

  // Handle session expiry
  const handleSessionExpiry = useCallback(() => {
    // Save current state before logging out
    BookingStateManager.saveLastBookingState({
      step: currentStep,
      flights: selectedFlights,
      pricing: pricingOption,
      passengerData,
      seatData,
      passengerCounts: {
        adults: passengers,
        children,
        infants,
      },
    });
    navigate("/session-expired");
  }, [currentStep, passengerData, seatData, selectedFlights, pricingOption, navigate]);

  // Try to recover state on mount
  useEffect(() => {
    const savedState = BookingStateManager.getBookingState();
    if (savedState) {
      setPassengerData(savedState.passengerData || null);
      setSeatData(savedState.seatData || null);
      setCurrentStep(savedState.step || "passenger-details");
      
      // Update the session timer
      dispatch({ 
        type: "UPDATE_LAST_ACTIVITY",
        payload: Date.now()
      });
    }
  }, [dispatch]);

  // Save state on important changes
  useEffect(() => {
    BookingStateManager.saveBookingState({
      currentStep,
      flights: selectedFlights,
      pricing: pricingOption,
      passengerData,
      seatData,
      passengerCounts: {
        adults: passengers,
        children,
        infants,
      },
    });
  }, [currentStep, passengerData, seatData]);

  return (
    <div className="container my-4">
      <div className="booking-progress mb-4">
        <div className="row text-center">
          <div
            className={`col-md-3 ${
              currentStep === "passenger-details" ? "active" : ""
            }`}
          >
            <div className="progress-step">
              <div className="step-number">1</div>
              <div className="step-title">Passenger Details</div>
            </div>
          </div>
          <div
            className={`col-md-3 ${
              currentStep === "seat-choice" || currentStep === "seat-selection"
                ? "active"
                : ""
            }`}
          >
            <div className="progress-step">
              <div className="step-number">2</div>
              <div className="step-title">Seat Selection</div>
            </div>
          </div>
          <div className="col-md-3">
            {" "}
            {/* Adjusted column width */}
            <div className="progress-step">
              <div className="step-number">3</div>
              <div className="step-title">Payment</div>
            </div>
          </div>
          <div className="col-md-3">
            {" "}
            {/* Adjusted column width */}
            <div className="progress-step">
              <div className="step-number">4</div>
              <div className="step-title">Confirmation</div>
            </div>
          </div>
        </div>
      </div>

      <div className="booking-summary card mb-4 shadow-sm">
        <div className="card-header bg-primary text-white py-3">
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">
              <i className="fas fa-receipt me-2"></i>
              Booking Summary
            </h4>
            <span className="badge bg-white text-primary fs-6">
              {String(pricingOption || "") === "flex" ||
              String(pricingOption || "") === "premium"
                ? "Refundable"
                : String(pricingOption || "") === "standard"
                  ? "Partially Refundable"
                  : "Non-refundable"}
            </span>
          </div>
        </div>

        <div className="card-body p-0">
          {/* Outbound Flight Card */}
          <div className="flight-summary-card p-3 border-bottom">
            <div className="d-flex align-items-center mb-3">
              <div className="flight-direction-badge bg-primary text-white px-3 py-1 rounded-pill me-3">
                <i className="fas fa-plane-departure me-2"></i>Outbound
              </div>
              <h5 className="mb-0">
                {selectedFlights?.outbound?.itineraries?.[0]?.segments?.[0]
                  ?.departure?.iataCode
                  ? String(
                      selectedFlights.outbound.itineraries[0].segments[0]
                        .departure.iataCode
                    )
                  : ""}{" "}
                →
                {selectedFlights?.outbound?.itineraries?.[0]?.segments?.[
                  selectedFlights.outbound.itineraries[0].segments.length - 1
                ]?.arrival?.iataCode
                  ? String(
                      selectedFlights.outbound.itineraries[0].segments[
                        selectedFlights.outbound.itineraries[0].segments
                          .length - 1
                      ].arrival.iataCode
                    )
                  : ""}
              </h5>
            </div>

            <div className="row">
              <div className="col-md-5">
                <div className="departure-details mb-3">
                  <div className="airport-code fs-5 fw-bold text-primary">
                    {selectedFlights?.outbound?.itineraries?.[0]?.segments?.[0]
                      ?.departure?.iataCode
                      ? String(
                          selectedFlights.outbound.itineraries[0].segments[0]
                            .departure.iataCode
                        )
                      : ""}
                  </div>
                  <div className="airport-name">
                    {formatAirportDisplay(
                      selectedFlights?.outbound?.itineraries?.[0]?.segments?.[0]
                        ?.departure?.iataCode,
                      "city" // formatAirportDisplay handles potential null/undefined
                    )}
                    ,{" "}
                    {formatAirportDisplay(
                      selectedFlights?.outbound?.itineraries?.[0]?.segments?.[0]
                        ?.departure?.iataCode,
                      "country" // formatAirportDisplay handles potential null/undefined
                    )}
                  </div>
                  <div className="departure-time text-muted">
                    <i className="far fa-clock me-1"></i>
                    {new Date(
                      String(
                        selectedFlights?.outbound?.itineraries?.[0]
                          ?.segments?.[0]?.departure?.at || ""
                      ) // Ensure it's a string
                    ).toLocaleString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                      month: "short",
                      day: "numeric",
                    })}
                  </div>
                </div>
              </div>

              <div className="col-md-2 text-center d-flex flex-column justify-content-center">
                <div className="flight-duration">
                  <i className="fas fa-clock me-1"></i>
                  {String(
                    selectedFlights?.outbound?.itineraries?.[0]?.duration || ""
                  ) // Ensure it's a string
                    .replace("PT", "")
                    .replace("H", "h ")
                    .replace("M", "m")}
                </div>
                <div className="flight-line position-relative my-2">
                  <hr className="m-0" />
                  <i
                    className="fas fa-plane text-primary position-absolute"
                    style={{
                      top: "-10px",
                      left: "50%",
                      transform: "translateX(-50%)",
                    }}
                  ></i>
                </div>
                <div className="flight-stops text-muted small">
                  {selectedFlights?.outbound?.itineraries?.[0]?.segments
                    ?.length === 1
                    ? "Direct Flight"
                    : `${
                        selectedFlights?.outbound?.itineraries?.[0]?.segments
                          ?.length - 1
                      } Stop${
                        selectedFlights?.outbound?.itineraries?.[0]?.segments
                          ?.length -
                          1 >
                        1
                          ? "s"
                          : ""
                      }`}
                </div>
              </div>

              <div className="col-md-5 text-end">
                <div className="arrival-details mb-3">
                  <div className="airport-code fs-5 fw-bold text-primary">
                    {selectedFlights?.outbound?.itineraries?.[0]?.segments?.[
                      selectedFlights.outbound.itineraries[0].segments.length -
                        1
                    ]?.arrival?.iataCode
                      ? String(
                          selectedFlights.outbound.itineraries[0].segments[
                            selectedFlights.outbound.itineraries[0].segments
                              .length - 1
                          ].arrival.iataCode
                        )
                      : ""}
                  </div>
                  <div className="airport-name">
                    {formatAirportDisplay(
                      selectedFlights?.outbound?.itineraries?.[0]?.segments?.[
                        selectedFlights?.outbound?.itineraries?.[0]?.segments // formatAirportDisplay handles potential null/undefined
                          ?.length - 1
                      ]?.arrival?.iataCode,
                      "city"
                    )}
                    ,{" "}
                    {formatAirportDisplay(
                      selectedFlights?.outbound?.itineraries?.[0]?.segments?.[
                        selectedFlights?.outbound?.itineraries?.[0]?.segments // formatAirportDisplay handles potential null/undefined
                          ?.length - 1
                      ]?.arrival?.iataCode,
                      "country"
                    )}
                  </div>
                  <div className="arrival-time text-muted">
                    <i className="far fa-clock me-1"></i>
                    {new Date(
                      String(
                        selectedFlights?.outbound?.itineraries?.[0]?.segments?.[
                          selectedFlights.outbound.itineraries[0].segments
                            .length - 1
                        ]?.arrival?.at || ""
                      ) // Ensure it's a string
                    ).toLocaleString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                      month: "short",
                      day: "numeric",
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Return Flight Card (if applicable) */}
          {selectedFlights?.return && (
            <div className="flight-summary-card p-3 border-bottom">
              <div className="d-flex align-items-center mb-3">
                <div className="flight-direction-badge bg-secondary text-white px-3 py-1 rounded-pill me-3">
                  <i className="fas fa-plane-arrival me-2"></i>Return
                </div>
                <h5 className="mb-0">
                  {selectedFlights?.return?.itineraries?.[0]?.segments?.[0]
                    ?.departure?.iataCode
                    ? String(
                        selectedFlights.return.itineraries[0].segments[0]
                          .departure.iataCode
                      )
                    : ""}{" "}
                  →
                  {selectedFlights?.return?.itineraries?.[0]?.segments?.[
                    selectedFlights.return.itineraries[0].segments.length - 1
                  ]?.arrival?.iataCode
                    ? String(
                        selectedFlights.return.itineraries[0].segments[
                          selectedFlights.return.itineraries[0].segments
                            .length - 1
                        ].arrival.iataCode
                      )
                    : ""}
                </h5>
              </div>

              <div className="row">
                <div className="col-md-5">
                  <div className="departure-details mb-3">
                    <div className="airport-code fs-5 fw-bold text-primary">
                      {selectedFlights?.return?.itineraries?.[0]?.segments?.[0]
                        ?.departure?.iataCode
                        ? String(
                            selectedFlights.return.itineraries[0].segments[0]
                              .departure.iataCode
                          )
                        : ""}
                    </div>
                    <div className="airport-name">
                      {formatAirportDisplay(
                        selectedFlights?.return?.itineraries?.[0]?.segments?.[0]
                          ?.departure?.iataCode,
                        "city" // formatAirportDisplay handles potential null/undefined
                      )}
                      ,{" "}
                      {formatAirportDisplay(
                        selectedFlights?.return?.itineraries?.[0]?.segments?.[0]
                          ?.departure?.iataCode,
                        "country" // formatAirportDisplay handles potential null/undefined
                      )}
                    </div>
                    <div className="departure-time text-muted">
                      <i className="far fa-clock me-1"></i>
                      {new Date(
                        String(
                          selectedFlights?.return?.itineraries?.[0]
                            ?.segments?.[0]?.departure?.at || ""
                        ) // Ensure it's a string
                      ).toLocaleString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                        month: "short",
                        day: "numeric",
                      })}
                    </div>
                  </div>
                </div>

                <div className="col-md-2 text-center d-flex flex-column justify-content-center">
                  <div className="flight-duration">
                    <i className="fas fa-clock me-1"></i>
                    {String(
                      selectedFlights?.return?.itineraries?.[0]?.duration || ""
                    ) // Ensure it's a string
                      .replace("PT", "")
                      .replace("H", "h ")
                      .replace("M", "m")}
                  </div>
                  <div className="flight-line position-relative my-2">
                    <hr className="m-0" />
                    <i
                      className="fas fa-plane text-primary position-absolute"
                      style={{
                        top: "-10px",
                        left: "50%",
                        transform: "translateX(-50%) rotate(180deg)",
                      }}
                    ></i>
                  </div>
                  <div className="flight-stops text-muted small">
                    {selectedFlights?.return?.itineraries?.[0]?.segments
                      ?.length === 1
                      ? "Direct Flight"
                      : `${
                          selectedFlights?.return?.itineraries?.[0]?.segments
                            ?.length - 1
                        } Stop${
                          selectedFlights?.return?.itineraries?.[0]?.segments
                            ?.length -
                            1 >
                          1
                            ? "s"
                            : ""
                        }`}
                  </div>
                </div>

                <div className="col-md-5 text-end">
                  <div className="arrival-details mb-3">
                    <div className="airport-code fs-5 fw-bold text-primary">
                      {selectedFlights?.return?.itineraries?.[0]?.segments?.[
                        selectedFlights.return.itineraries[0].segments.length -
                          1
                      ]?.arrival?.iataCode
                        ? String(
                            selectedFlights.return.itineraries[0].segments[
                              selectedFlights.return.itineraries[0].segments
                                .length - 1
                            ].arrival.iataCode
                          )
                        : ""}
                    </div>
                    <div className="airport-name">
                      {formatAirportDisplay(
                        selectedFlights?.return?.itineraries?.[0]?.segments?.[
                          selectedFlights?.return?.itineraries?.[0]?.segments // formatAirportDisplay handles potential null/undefined
                            ?.length - 1
                        ]?.arrival?.iataCode,
                        "city"
                      )}
                      ,{" "}
                      {formatAirportDisplay(
                        selectedFlights?.return?.itineraries?.[0]?.segments?.[
                          selectedFlights?.return?.itineraries?.[0]?.segments // formatAirportDisplay handles potential null/undefined
                            ?.length - 1
                        ]?.arrival?.iataCode,
                        "country"
                      )}
                    </div>
                    <div className="arrival-time text-muted">
                      <i className="far fa-clock me-1"></i>
                      {new Date(
                        String(
                          selectedFlights?.return?.itineraries?.[0]?.segments?.[
                            selectedFlights.return.itineraries[0].segments
                              .length - 1
                          ]?.arrival?.at || ""
                        ) // Ensure it's a string
                      ).toLocaleString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                        month: "short",
                        day: "numeric",
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Passenger and Seat Details */}
          {(passengerData || seatData) && (
            <div className="p-3 border-top">
              {passengerData && (
                <div className="mb-3">
                  <h6 className="text-primary">
                    <i className="fas fa-users me-2"></i>Passengers
                  </h6>
                  <ul className="list-unstyled">
                    {passengerData.map((p, i) => (
                      <li key={i}>
                        {p.firstName} {p.lastName} ({p.type})
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {seatData && (
                <div>
                  <h6 className="text-primary">
                    <i className="fas fa-chair me-2"></i>Seat Selections
                  </h6>
                  {/* You can add more detailed seat info here */}
                  <p>Seats selected. (Details can be added)</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Render steps based on currentStep */}
      {currentStep === "passenger-details" && (
        <PassengerDetails
          adults={passengers}
          children={children}
          infants={infants}
          onComplete={handlePassengerDetailsComplete}
        />
      )}

      {currentStep === "seat-selection" &&
        selectedFlights?.outbound &&
        Array.isArray(passengerData) && (
          <SeatSelection
            selectedFlights={selectedFlights}
            passengers={passengerData}
            pricingOption={pricingOption || "standard"}
            onComplete={handleSeatSelectionComplete}
          />
        )}

      {currentStep === "payment" && (
        <Payment
          totalAmount={calculateTotalPrice()}
          onPaymentSuccess={handlePaymentComplete}
        />
      )}

      {showTimeoutWarning && (
        <SessionTimeoutModal
          onReactivate={handleSessionReactivate}
          onLogout={handleSessionExpiry}
        />
      )}
    </div>
  );
};

export default BookingPage;
