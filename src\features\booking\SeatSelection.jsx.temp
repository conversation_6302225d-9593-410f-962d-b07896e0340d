import React, { useState, useEffect, useCallback, useMemo } from "react";
import { PriceCalculationService } from "../../services/priceCalculationService";
import { SeatMapService } from "../../services/seatMapService";
import { getSeatSuggestions, getSuggestedSeats } from "../../utils/seatUtils";
import { formatPrice } from "../../utils/formatUtils";
import { throttle } from "../../utils/performanceUtils";
import "../../assets/styles/Booking.css";
import "../../assets/styles/SeatMap.css";

/**
 * Component for seat selection during the booking process
 */
const SeatSelection = ({
  passengers,
  selectedFlights,
  pricingOption = "standard",
  onComplete,
}) => {  // State management
  const [selectedSeats, setSelectedSeats] = useState({
    outbound: {},
    return: selectedFlights.return ? {} : null,
  });
  const [seatMaps, setSeatMaps] = useState({
    outbound: null,
    return: null,
  });
  const [errors, setErrors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  const [refreshing, setRefreshing] = useState(false);

  // Memoized passenger groups for better seating suggestions
  const passengerGroups = useMemo(() => {
    return passengers.reduce((groups, passenger) => {
      const key = passenger.associatedAdult || passenger.groupId || passenger.lastName;
      if (!groups[key]) groups[key] = [];
      groups[key].push(passenger);
      return groups;
    }, {});
  }, [passengers]);

  // Throttled seat map refresh function
  const refreshSeatMaps = useCallback(
    throttle(async () => {
      if (refreshing) return;
      setRefreshing(true);

      try {
        const outboundMap = await SeatMapService.getSeatMap(selectedFlights.outbound.id);
        const returnMap = selectedFlights.return 
          ? await SeatMapService.getSeatMap(selectedFlights.return.id)
          : null;

        setSeatMaps({
          outbound: outboundMap,
          return: returnMap,
        });
        setLastUpdate(Date.now());
      } catch (error) {
        console.error("Failed to refresh seat maps:", error);
        setErrors(prev => [...prev, "Failed to get latest seat availability."]);
      } finally {
        setRefreshing(false);
      }
    }, 10000), // Throttle to at most once every 10 seconds
    [selectedFlights, refreshing]
  );

  // Initial seat map fetch on component mount  useEffect(() => {
    const fetchInitialSeatMaps = async () => {
      try {
        const outboundMap = await SeatMapService.getSeatMap(selectedFlights.outbound.id);
        const returnMap = selectedFlights.return 
          ? await SeatMapService.getSeatMap(selectedFlights.return.id)
          : null;

        setSeatMaps({
          outbound: outboundMap,
          return: returnMap,
        });
        setLastUpdate(Date.now());
      } catch (error) {
        console.error("Failed to fetch initial seat maps:", error);
        setErrors(["Failed to load seat maps. Please try again."]);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialSeatMaps();

    // Set up periodic refresh
    const refreshInterval = setInterval(refreshSeatMaps, 30000); // Refresh every 30 seconds
    return () => clearInterval(refreshInterval);
  }, [selectedFlights, refreshSeatMaps]);

  // Listen for seat map updates and validate existing selections
  useEffect(() => {
    // Skip if this is the first load
    if (!lastUpdate) return;

    // Validate all current seat selections against new seat map
    const validateExistingSelections = (flightType) => {
      if (!selectedSeats[flightType] || !seatMaps[flightType]) return;

      Object.entries(selectedSeats[flightType]).forEach(([seatNumber, passengerId]) => {
        const passenger = passengers.find(p => p.id === passengerId);
        const seat = seatMaps[flightType].flat().find(s => s.number === seatNumber);

        if (!seat?.available) {
          // Seat is no longer available, remove it
          setSelectedSeats(prev => {
            const updated = { ...prev };
            delete updated[flightType][seatNumber];
            return updated;
          });
          setErrors(prev => [
            ...prev,
            `Seat ${seatNumber} is no longer available for ${passenger?.firstName || 'passenger'}. Please select another seat.`
          ]);
        }
      });
    };

    validateExistingSelections('outbound');
    if (selectedFlights.return) {
      validateExistingSelections('return');
    }
  }, [seatMaps, lastUpdate]);

  // State to track if there's an issue with the passengers prop
  const [hasPassengerError, setHasPassengerError] = useState(false);

  // New state for tracking specific validation rules
  const [classValidation, setClassValidation] = useState({
    premiumInfantCount: 0,
    mixedClassWarnings: [],
  });

  const [suggestedSeats, setSuggestedSeats] = useState([]);
  const [currentPassenger, setCurrentPassenger] = useState(null);
  const [seatPreferences, setSeatPreferences] = useState({
    seatType: "window",
  });

  // Check if passengers prop is valid on mount or when it changes
  useEffect(() => {
    if (!Array.isArray(passengers)) {
      console.error(
        "SeatSelection received invalid passengers prop:",
        passengers
      );
      setHasPassengerError(true);
    } else {
      setHasPassengerError(false);
    }
  }, [passengers]);

  useEffect(() => {
    if (currentPassenger) {
      const suggestions = getSeatSuggestions(
        currentPassenger,
        seatMaps[currentPassenger.flight],
        selectedSeats[currentPassenger.flight] || []
      );
      setSuggestedSeats({ [currentPassenger.id]: suggestions });
    }
  }, [currentPassenger, seatMaps, selectedSeats]);

  useEffect(() => {
    // Fetch seat map logic...
    // After fetching seat map:
    const suggestions = getSuggestedSeats(
      seatMaps.outbound,
      passengers.length,
      seatPreferences
    );
    setSuggestedSeats(suggestions);
  }, [seatMaps, passengers.length, seatPreferences]);

  const handlePreferenceChange = (preference) => {
    setSeatPreferences((prev) => ({ ...prev, ...preference }));
  };

  /**
   * Generate a seat map for a flight
   */
  function generateSeatMap(flight) {
    if (!flight || !flight.itineraries || flight.itineraries.length === 0) {
      console.warn("generateSeatMap received invalid flight data:", flight);
      return []; // Return empty array for invalid data
    }
    // This is a simplified seat map generation
    // In a real app, this would come from an API
    const rows = 30;
    const seatsPerRow = 6;
    const seatMap = [];

    for (let row = 1; row <= rows; row++) {
      const seatRow = [];
      for (let seat = 0; seat < seatsPerRow; seat++) {
        const seatLetter = String.fromCharCode(65 + seat);
        const seatNumber = `${row}${seatLetter}`;
        seatRow.push({
          id: seatNumber,
          number: seatNumber,
          available: Math.random() > 0.3, // Randomly mark some seats as unavailable
          price: calculateSeatPrice(row, seat, pricingOption),
          type: getSeatType(row, seat),
        });
      }
      seatRow.push({
        // Add aisle placeholder
        id: `aisle-${row}`,
        number: "",
        available: false,
        price: 0,
        type: "AISLE",
      });
      for (let seat = 6; seat < 12; seat++) {
        // Assuming a wider body with more seats
        const seatLetter = String.fromCharCode(65 + seat);
        const seatNumber = `${row}${seatLetter}`;
        seatRow.push({
          id: seatNumber,
          number: seatNumber,
          available: Math.random() > 0.3, // Randomly mark some seats as unavailable
          price: calculateSeatPrice(row, seat, pricingOption),
          type: getSeatType(row, seat),
        });
      }
      seatMap.push(seatRow);
    }

    return seatMap;
  }

  /**
   * Calculate price for a seat based on its location and pricing option
   */
  function calculateSeatPrice(row, seat, pricingOption) {
    return PriceCalculationService.calculateSeatPrice(row, seat, pricingOption);
  }

  /**
   * Get the type of seat based on its location
   */
  function getSeatType(row, seat) {
    if (row <= 5) return "PREMIUM";
    if (row >= 12 && row <= 14) return "EXIT";
    return "STANDARD";
  }

  /**
   * Check if a seat is selectable based on availability and other conditions
   */
  function isSeatSelectable(seat, flightType) {
    // Check if the seat exists and is available
    if (!seat || !seat.available) {
      return false;
    }

    // Check if the seat is already selected by another passenger
    if (selectedSeats[flightType] && selectedSeats[flightType][seat.number]) {
      return false;
    }

    return true;
  }
  /**
   * Handle seat selection with group optimization
   */
  const handleSeatSelect = async (flightType, seatNumber, passengerId) => {
    if (!flightType || !seatMaps[flightType]) return;

    // Get fresh seat map if it's been more than 10 seconds
    if (Date.now() - lastUpdate > 10000) {
      await refreshSeatMaps();
    }

    const selectedPassenger = passengers.find(p => p.id === passengerId);
    if (!selectedPassenger) return;

    // Find the selected seat in the seat map
    const selectedSeat = seatMaps[flightType]
      .flat()
      .find(seat => seat.number === seatNumber);

    // Double check availability
    if (!selectedSeat?.available) {
      setErrors([`Seat ${seatNumber} is no longer available. Please select another seat.`]);
      return;
    }

    // Validate the selection
    const validationErrors = SeatMapService.validateSeatSelection(
      selectedPassenger,
      selectedSeat,
      selectedSeats[flightType]
    );

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setSelectedSeats(prev => {
      const updated = { ...prev };
      if (!updated[flightType]) updated[flightType] = {};

      // If selecting empty seat option, remove current seat
      if (seatNumber === "") {
        Object.entries(updated[flightType]).forEach(([key, id]) => {
          if (id === passengerId) {
            delete updated[flightType][key];
          }
        });
        return updated;
      }

      // Remove passenger's current seat if they have one
      Object.entries(updated[flightType]).forEach(([key, id]) => {
        if (id === passengerId) {
          delete updated[flightType][key];
        }
      });

      // Assign new seat
      updated[flightType][seatNumber] = passengerId;

      // If passenger is part of a group, try to find seats for other group members
      const group = Object.values(passengerGroups).find(g => 
        g.some(p => p.id === passengerId)
      );

      if (group && group.length > 1) {
        const unassignedGroupMembers = group.filter(p => 
          p.id !== passengerId && !Object.values(updated[flightType]).includes(p.id)
        );

        if (unassignedGroupMembers.length > 0) {
          const suggestions = getSuggestedSeats(
            seatMaps[flightType],
            unassignedGroupMembers.length,
            { nearSeat: seatNumber }
          );

          suggestions.forEach((suggestedSeat, index) => {
            if (
              suggestedSeat && 
              !Object.keys(updated[flightType]).includes(suggestedSeat) &&
              index < unassignedGroupMembers.length
            ) {
              updated[flightType][suggestedSeat] = unassignedGroupMembers[index].id;
            }
          });
        }
      }

      return updated;
    });

    // Handle smart seat suggestion for remaining passengers
    const remainingPassengers = passengers.filter(p => !selectedSeats[flightType][p.id]);
    remainingPassengers.forEach(p => {
      const suggestion = getSeatSuggestion(p, flightType);
      if (suggestion) {
        handleSeatSelect(flightType, suggestion.number, p.id);
      }
    });
  };
  /**
   * Validate seat selections for all passengers
   */
  const validateSeats = () => {
    const validationErrors = [];

    // Check if seat maps are loaded
    if (!seatMaps.outbound) {
      validationErrors.push("Seat map not available. Please try again.");
      return false;
    }

    // Validate each passenger's seat assignments
    passengers.forEach(currentPassenger => {
      // Outbound flight validation
      const outboundSeat = selectedSeats.outbound[currentPassenger.id];
      if (!outboundSeat) {
        validationErrors.push(`${currentPassenger.firstName} needs an outbound seat`);
      } else {
        const seatDetails = seatMaps.outbound
          .flat()
          .find(s => s.number === outboundSeat);

        if (!seatDetails?.available) {
          validationErrors.push(
            `Selected seat ${outboundSeat} for ${currentPassenger.firstName} is no longer available`
          );
        } else {
          const seatErrors = SeatMapService.validateSeatSelection(
            currentPassenger,
            seatDetails,
            selectedSeats.outbound
          );
          validationErrors.push(...seatErrors);
        }
      }

      // Return flight validation if applicable
      if (selectedFlights.return) {
        const returnSeat = selectedSeats.return?.[currentPassenger.id];
        if (!returnSeat) {
          validationErrors.push(`${currentPassenger.firstName} needs a return seat`);
        } else {
          const seatDetails = seatMaps.return
            .flat()
            .find(s => s.number === returnSeat);

          if (!seatDetails?.available) {
            validationErrors.push(
              `Selected seat ${returnSeat} for ${currentPassenger.firstName} is no longer available`
            );
          } else {
            const seatErrors = SeatMapService.validateSeatSelection(
              currentPassenger,
              seatDetails,
              selectedSeats.return
            );
            validationErrors.push(...seatErrors);
          }
        }
      }
    });

    // Family group validations
    const familyGroups = groupPassengersByFamily(passengers);
    familyGroups.forEach(group => {
      if (group.length > 1) {
        // Check cabin class consistency
        const outboundClasses = new Set(
          group.map(familyMember => {
            const seat = Object.entries(selectedSeats.outbound).find(
              ([_, id]) => id === familyMember.id
            )?.[0];
            return getSeatClass(seat);
          })
        );

        if (outboundClasses.size > 1) {
          const warning = `Family group ${group[0].lastName} has mixed cabin classes`;
          setClassValidation(prev => ({
            ...prev,
            mixedClassWarnings: [...prev.mixedClassWarnings, warning]
          }));
        }

        // Check return flight cabin classes
        if (selectedFlights.return) {
          const returnClasses = new Set(
            group.map(familyMember => {
              const seat = Object.entries(selectedSeats.return || {}).find(
                ([_, id]) => id === familyMember.id
              )?.[0];
              return getSeatClass(seat);
            })
          );

          if (returnClasses.size > 1) {
            const warning = `Family group ${group[0].lastName} has mixed cabin classes on return flight`;
            setClassValidation(prev => ({
              ...prev,
              mixedClassWarnings: [...prev.mixedClassWarnings, warning]
            }));
          }
        }
      }
    });

    // Premium cabin infant restrictions
    if (classValidation.premiumInfantCount > 1) {
      validationErrors.push("Maximum of 1 infant allowed in premium cabins");
    }

    setErrors(validationErrors);
    return validationErrors.length === 0;
  };

  // Helper function to determine if a seat is in premium cabin
  const isPremiumSeat = (seatNumber) => {
    const row = parseInt(seatNumber.match(/\d+/)[0]);
    return row <= 4; // Assuming rows 1-4 are premium
  };

  // Helper function to get seat class
  const getSeatClass = (seatNumber) => {
    return isPremiumSeat(seatNumber) ? "PREMIUM" : "ECONOMY";
  };

  // Helper function to group passengers by family (same last name)
  const groupPassengersByFamily = (passengers) => {
    const families = {};
    passengers.forEach((passenger) => {
      if (!families[passenger.lastName]) {
        families[passenger.lastName] = [];
      }
      families[passenger.lastName].push(passenger);
    });
    return Object.values(families);
  };

  /**
   * Check if two seats are adjacent
   */
  const areSeatsAdjacent = (seat1, seat2) => {
    if (!seat1 || !seat2) return false;

    const matches1 = seat1.match(/(\d+)([A-Z])/);
    const matches2 = seat2.match(/(\d+)([A-Z])/);

    if (!matches1 || !matches2) return false;

    const row1 = parseInt(matches1[1]);
    const row2 = parseInt(matches2[1]);
    const letter1 = matches1[2];
    const letter2 = matches2[2];

    // Same row check
    if (row1 === row2) {
      const seatDiff = Math.abs(letter1.charCodeAt(0) - letter2.charCodeAt(0));

      // Check for regular adjacent seats
      if (seatDiff === 1) return true;

      // Check for seats across the aisle
      // For 3-3 configuration: C-D are adjacent
      if (
        (letter1 === "C" && letter2 === "D") ||
        (letter1 === "D" && letter2 === "C")
      ) {
        return true;
      }

      // For 3-3-3 configuration: Additional F-G adjacency
      if (
        (letter1 === "F" && letter2 === "G") ||
        (letter1 === "G" && letter2 === "F")
      ) {
        return true;
      }
    }

    return false;
  };

  /**
   * Get optimal seats for a passenger based on criteria
   */
  const getOptimalSeats = (passenger, flightType) => {
    const availableSeats = seatMaps[flightType]
      .flat()
      .filter(
        (seat) =>
          seat.available &&
          seat.type !== "AISLE" &&
          !Object.keys(selectedSeats[flightType] || {}).includes(seat.number)
      );

    // Special handling for infants and their accompanying adults
    if (passenger.type === "INFANT") {
      // Avoid exit rows and prefer standard seats
      return availableSeats.filter(
        (seat) => seat.type !== "EXIT" && seat.type !== "PREMIUM"
      );
    }

    // Families with children - prefer middle section
    if (passenger.type === "CHILD") {
      const middleSection = availableSeats.filter((seat) => {
        const letter = seat.number.slice(-1);
        return ["D", "E", "F"].includes(letter);
      });
      return middleSection.length ? middleSection : availableSeats;
    }

    // Adults who are traveling with infants
    if (
      passenger.type === "ADULT" &&
      passengers.some(
        (p) => p.type === "INFANT" && p.associatedAdult === passenger.id
      )
    ) {
      // Prefer aisle seats for easy access
      const aisleSeats = availableSeats.filter((seat) => {
        const letter = seat.number.slice(-1);
        return ["C", "D"].includes(letter);
      });
      return aisleSeats.length ? aisleSeats : availableSeats;
    }

    // Regular adults - prioritize based on pricing option
    if (pricingOption === "premium") {
      const premiumSeats = availableSeats.filter(
        (seat) => seat.type === "PREMIUM"
      );
      return premiumSeats.length ? premiumSeats : availableSeats;
    }

    return availableSeats;
  };

  /**
   * Get a suggested seat for a passenger
   */
  const getSeatSuggestion = (passenger, flightType) => {
    const optimalSeats = getOptimalSeats(passenger, flightType);
    if (!optimalSeats.length) return null;

    // Sort by optimal criteria
    return optimalSeats.sort((a, b) => {
      // Prioritize keeping families together
      const familyMembers = passengers.filter(
        (p) => p.lastName === passenger.lastName
      );
      if (familyMembers.length > 1) {
        const familySeats = Object.entries(selectedSeats[flightType] || {})
          .filter(([_, id]) => familyMembers.some((fm) => fm.id === id))
          .map(([seat]) => seat);

        if (familySeats.length) {
          const aDistance = Math.min(
            ...familySeats.map((seat) =>
              Math.abs(
                getSeatRowAndNumber(seat)[0] - getSeatRowAndNumber(a.number)[0]
              )
            )
          );
          const bDistance = Math.min(
            ...familySeats.map((seat) =>
              Math.abs(
                getSeatRowAndNumber(seat)[0] - getSeatRowAndNumber(b.number)[0]
              )
            )
          );
          if (aDistance !== bDistance) return aDistance - bDistance;
        }
      }

      // Then consider price
      return a.price - b.price;
    })[0];
  };

  /**
   * Handle form submission
   */ const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form submitted. Validating seats...");

    const isValid = validateSeats();

    if (!isValid) {
      console.log(
        "Seat validation failed. Please ensure all passengers have seats assigned."
      );
      // Scroll to error messages if present
      const errorElement = document.querySelector(".alert-danger");
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: "smooth", block: "start" });
      }
      return;
    }

    // All validations passed, prepare seat data
    const seatData = {
      seatSelections: selectedSeats,
      totalSeatCost: calculateTotalSeatCost(),
      // Add detailed seat assignments for booking record
      assignments: {
        outbound: Object.entries(selectedSeats.outbound).map(
          ([seat, passengerId]) => ({
            seat,
            passengerId,
            passenger:
              passengers.find((p) => p.id === passengerId)?.firstName || "",
          })
        ),
        return: selectedFlights.return
          ? Object.entries(selectedSeats.return).map(([seat, passengerId]) => ({
              seat,
              passengerId,
              passenger:
                passengers.find((p) => p.id === passengerId)?.firstName || "",
            }))
          : [],
      },
    };

    console.log(
      "Seat validation passed. Submitting seat selections:",
      seatData
    );
    onComplete(seatData);
  };

  /**
   * Calculate total cost of selected seats
   */
  const calculateTotalSeatCost = () => {
    if (!selectedSeats || (!selectedSeats.outbound && !selectedSeats.return)) {
      return 0;
    }

    const priceDetails = PriceCalculationService.calculateTotalPrice({
      flights: selectedFlights,
      passengers: {}, // Empty since we only want seat costs
      pricingOption,
      seatSelections: selectedSeats,
    });

    return priceDetails.seatTotal;
  };

  /**
   * Extract row and seat number from seat ID
   */
  const getSeatRowAndNumber = (seatId) => {
    if (!seatId) return [0, 0];
    const matches = seatId.match(/(\d+)([A-Z])/);
    if (!matches) return [0, 0];
    const row = parseInt(matches[1]);
    const letter = matches[2];
    const seat = letter.charCodeAt(0) - 65;
    return [row, seat];
  };

  // Handle keyboard navigation
  const handleKeyPress = useCallback(
    (e, flightType, seatNumber, passengerId) => {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        handleSeatSelect(flightType, seatNumber, passengerId);
      }
    },
    []
  );

  // Get seat description for accessibility
  const getSeatDescription = (seat) => {
    const features = [];
    if (seat.type === "PREMIUM") features.push("Premium class");
    if (seat.type === "EXIT") features.push("Exit row");
    features.push(seat.available ? "Available" : "Unavailable");
    if (seat.price > 0) features.push(`Price: $${seat.price}`);
    return features.join(", ");
  };

  const renderFlightDirection = (isReturn = false) => (
    <div
      className="flight-direction"
      role="img"
      aria-label={`${isReturn ? "Return" : "Outbound"} flight direction`}
    >
      <i className={`fas fa-plane ${isReturn ? "fa-rotate-180" : ""}`}></i>
      <span>{isReturn ? "Return Flight" : "Outbound Flight"}</span>
    </div>
  );

  const renderCabinSeparator = (label) => (
    <div
      className="cabin-separator"
      role="separator"
      aria-label={`${label} cabin separator`}
    >
      <div className="text-center text-muted small">{label}</div>
    </div>
  );

  const renderSeat = (seat, rowIndex, seatIndex, flightType) => {
    const isOccupied = seat.occupied;
    const isSuggested = suggestedSeats.some(
      (s) => s.row === rowIndex && s.col === seatIndex
    );

    return (
      <button
        className={`seat-btn ${seat.type} ${isSuggested ? "suggested" : ""}`}
        disabled={!isSeatSelectable(seat, rowIndex, seatIndex, flightType)}
        onClick={() => handleSeatSelection(rowIndex, seatIndex, flightType)}
        aria-label={generateSeatAriaLabel(
          seat,
          rowIndex,
          seatIndex,
          isSuggested
        )}
        data-tooltip={generateSeatTooltip(seat, isSuggested)}
      >
        <span className="seat-label">{`${rowIndex + 1}${String.fromCharCode(65 + seatIndex)}`}</span>
        {seat.price && <small>{formatPrice(seat.price)}</small>}
        {isSuggested && (
          <span className="suggestion-indicator" aria-hidden="true">
            ✨
          </span>
        )}
      </button>
    );
  };

  const generateSeatAriaLabel = (seat, row, seatIndex, isSuggested) => {
    const seatNumber = `${row + 1}${String.fromCharCode(65 + seatIndex)}`;
    const seatType = seat.type.charAt(0).toUpperCase() + seat.type.slice(1);
    const price = seat.price ? `, price ${formatPrice(seat.price)}` : "";
    const suggestion = isSuggested ? ", Suggested seat" : "";
    return `Seat ${seatNumber}, ${seatType} class${price}${suggestion}`;
  };

  const generateSeatTooltip = (seat, isSuggested) => {
    let tooltip = `${seat.type.charAt(0).toUpperCase() + seat.type.slice(1)} Seat`;
    if (seat.price) tooltip += ` - ${formatPrice(seat.price)}`;
    if (seat.features?.length) tooltip += `\n${seat.features.join(", ")}`;
    if (isSuggested) tooltip += "\n✨ Recommended for you";
    return tooltip;
  };

  return (
    <div
      className="seat-selection-container"
      role="region"
      aria-label="Seat Selection"
    >
      <h3 id="seat-selection-title">Select Your Seats</h3>
      <p className="text-muted mb-4" id="seat-selection-description">
        Choose your preferred seats for each flight. Additional charges may
        apply.
      </p>

      {hasPassengerError && (
        <div className="alert alert-danger">
          Error: Invalid passenger data received for seat selection. Please
          return to the previous step.
        </div>
      )}

      {!hasPassengerError && errors.length > 0 && (
        <div className="alert alert-danger">
          <ul className="mb-0">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {!hasPassengerError && classValidation.mixedClassWarnings.length > 0 && (
        <div className="alert alert-warning">
          <h5>Mixed Cabin Class Warnings:</h5>
          <ul>
            {classValidation.mixedClassWarnings.map((warning, index) => (
              <li key={index}>{warning}</li>
            ))}
          </ul>
          <p>
            Consider selecting seats in the same cabin class for family members.
          </p>
        </div>
      )}

      {!hasPassengerError && (
        <form onSubmit={handleSubmit} aria-labelledby="seat-selection-title">
          {/* Outbound Flight Seat Map */}
          <div className="card mb-4">
            <div className="card-header bg-primary text-white">
              <h5 className="mb-0" id="outbound-seats-title">
                <i
                  className="fas fa-plane-departure me-2"
                  aria-hidden="true"
                ></i>
                Outbound Flight Seats
              </h5>
            </div>
            <div className="card-body">
              {renderFlightDirection()}
              <div
                className="seat-map"
                role="grid"
                aria-labelledby="outbound-seats-title"
              >
                {seatMaps.outbound.map((row, rowIndex) => {
                  // Add cabin separator after premium section (row 4)
                  const showSeparator = rowIndex === 4;
                  return (
                    <React.Fragment key={rowIndex}>
                      {showSeparator &&
                        renderCabinSeparator("Premium Cabin End")}
                      <div
                        className="seat-row"
                        role="row"
                        aria-rowindex={rowIndex + 1}
                      >
                        {row.map((seat, seatIndex) =>
                          renderSeat(seat, rowIndex, seatIndex, "outbound")
                        )}
                      </div>
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Return Flight Seat Map */}
          {selectedFlights.return && (
            <div className="card mb-4">
              <div className="card-header bg-secondary text-white">
                <h5 className="mb-0" id="return-seats-title">
                  <i className="fas fa-plane-arrival me-2"></i>
                  Return Flight Seats
                </h5>
              </div>
              <div className="card-body">
                {renderFlightDirection(true)}
                <div
                  className="seat-map"
                  role="grid"
                  aria-labelledby="return-seats-title"
                >
                  {seatMaps.return.map((row, rowIndex) => {
                    const showSeparator = rowIndex === 4;
                    return (
                      <React.Fragment key={rowIndex}>
                        {showSeparator &&
                          renderCabinSeparator("Premium Cabin End")}
                        <div
                          className="seat-row"
                          role="row"
                          aria-rowindex={rowIndex + 1}
                        >
                          {row.map((seat, seatIndex) =>
                            renderSeat(seat, rowIndex, seatIndex, "return")
                          )}
                        </div>
                      </React.Fragment>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Passenger Seat Assignment */}
          <div className="card mb-4">
            <div className="card-header">
              <h5 className="mb-0">Passenger Seat Assignment</h5>
            </div>
            <div className="card-body">
              {passengers.map((passenger) => (
                <div key={passenger.id} className="passenger-seat-row mb-3">
                  <h6>
                    {passenger.firstName} {passenger.lastName}
                    <span
                      className={`badge ms-2 ${
                        passenger.type === "ADULT"
                          ? "bg-primary"
                          : passenger.type === "CHILD"
                            ? "bg-info"
                            : "bg-warning text-dark"
                      }`}
                    >
                      {passenger.type} (ID: {passenger.id})
                    </span>
                  </h6>
                  <div className="row">
                    <div className="col-md-6">
                      {" "}
                      <select
                        className="form-select"
                        value={
                          Object.keys(selectedSeats.outbound || {}).find(
                            (seat) =>
                              selectedSeats.outbound[seat] === passenger.id
                          ) || ""
                        }
                        onChange={(e) =>
                          handleSeatSelect(
                            "outbound",
                            e.target.value,
                            passenger.id
                          )
                        }
                        required
                      >
                        <option value="">Select Outbound Seat</option>
                        {seatMaps.outbound
                          .flat()
                          .filter(
                            (seat) =>
                              seat.available &&
                              seat.type !== "AISLE" &&
                              (!Object.keys(
                                selectedSeats.outbound || {}
                              ).includes(seat.number) ||
                                selectedSeats.outbound[seat.number] ===
                                  passenger.id)
                          )
                          .map((seat) => (
                            <option key={seat.number} value={seat.number}>
                              {seat.number} - ${seat.price}
                            </option>
                          ))}
                      </select>
                    </div>
                    {selectedFlights.return && (
                      <div className="col-md-6">
                        {" "}
                        <select
                          className="form-select"
                          value={
                            Object.keys(selectedSeats.return || {}).find(
                              (seat) =>
                                selectedSeats.return[seat] === passenger.id
                            ) || ""
                          }
                          onChange={(e) =>
                            handleSeatSelect(
                              "return",
                              e.target.value,
                              passenger.id
                            )
                          }
                          required
                        >
                          <option value="">Select Return Seat</option>
                          {seatMaps.return
                            .flat()
                            .filter(
                              (seat) =>
                                seat.available &&
                                seat.type !== "AISLE" &&
                                (!Object.keys(
                                  selectedSeats.return || {}
                                ).includes(seat.number) ||
                                  selectedSeats.return[seat.number] ===
                                    passenger.id)
                            )
                            .map((seat) => (
                              <option key={seat.number} value={seat.number}>
                                {seat.number} - ${seat.price}
                              </option>
                            ))}
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Legend */}
          <div className="card mb-4">
            <div className="card-body">
              <h6>Seat Legend</h6>
              <div className="seat-legend">
                <div className="legend-item">
                  <span className="seat-indicator standard"></span>
                  Standard Seat
                </div>
                <div className="legend-item">
                  <span className="seat-indicator premium"></span>
                  Premium Seat
                </div>
                <div className="legend-item">
                  <span className="seat-indicator exit"></span>
                  Exit Row
                </div>
                <div className="legend-item">
                  <span className="seat-indicator selected"></span>
                  Selected
                </div>
                <div className="legend-item">
                  <span className="seat-indicator unavailable"></span>
                  Unavailable
                </div>
                <div className="legend-item">
                  <span className="seat-indicator infant-seat"></span>
                  Infant Seat
                </div>
              </div>
            </div>
          </div>

          <div className="d-grid gap-2 mt-4">
            <button type="submit" className="btn btn-primary">
              <i className="fas fa-credit-card me-2"></i>
              Continue to Payment
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default SeatSelection;
