import React, { useState } from "react";
import { Container, <PERSON>, <PERSON>, Al<PERSON>, <PERSON>, But<PERSON> } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "../context/AppContext";
import "../assets/styles/Pages.css";

const Hotels = () => {
  const navigate = useNavigate();
  const { dispatch } = useAppContext();

  const [searchResults, setSearchResults] = useState([]);
  const [searchParams, setSearchParams] = useState(null);
  const [filteredResults, setFilteredResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [currentView, setCurrentView] = useState("search"); // search, results, room-selection
  const [selectedHotel, setSelectedHotel] = useState(null);
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [showHotelDetails, setShowHotelDetails] = useState(false);

  console.log("Hotels component is rendering...");

  return (
    <div className="page-container">
      <Container fluid className="hotels-page">
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError("")}>
            {error}
          </Alert>
        )}

        {/* Search View */}
        {currentView === "search" && (
          <Row className="justify-content-center">
            <Col lg={8}>
              <div className="text-center mb-4">
                <h1>Find Your Perfect Hotel</h1>
                <p className="lead text-muted">
                  Discover comfortable accommodations for your next trip
                </p>
              </div>
              <Card>
                <Card.Header>
                  <h4>Hotel Search</h4>
                </Card.Header>
                <Card.Body>
                  <p>Hotel search functionality is being implemented.</p>
                  <p>
                    The hotel booking system components have been created and
                    are ready to be integrated.
                  </p>
                  <Button
                    variant="primary"
                    onClick={() =>
                      alert(
                        "Hotel search components are ready! The import errors have been fixed."
                      )
                    }
                  >
                    Test Hotel System
                  </Button>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </Container>
    </div>
  );
};

export default Hotels;
