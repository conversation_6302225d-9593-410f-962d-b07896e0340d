import React, { useState, useEffect } from 'react';
import SeatSelection from '../features/booking/SeatSelection';

const SeatModification = ({ booking, onSubmit, onCancel }) => {
  const [seatSelections, setSeatSelections] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Initialize with current seat assignments if they exist
    if (booking?.seatData?.seatSelections) {
      setSeatSelections(booking.seatData.seatSelections);
    }
  }, [booking]);

  const handleSeatSelectionComplete = (seatData) => {
    if (!seatData || !seatData.seatSelections) {
      setError('Invalid seat selection data');
      return;
    }

    // Format seat assignments for the API
    const seatAssignments = [];
    
    // Process outbound flight seats
    if (seatData.seatSelections.outbound) {
      Object.entries(seatData.seatSelections.outbound).forEach(([seat, passengerId]) => {
        seatAssignments.push({
          segmentId: booking.selectedFlights.outbound.id,
          seat,
          traveler: passengerId
        });
      });
    }

    // Process return flight seats if exists
    if (booking.selectedFlights.return && seatData.seatSelections.return) {
      Object.entries(seatData.seatSelections.return).forEach(([seat, passengerId]) => {
        seatAssignments.push({
          segmentId: booking.selectedFlights.return.id,
          seat,
          traveler: passengerId
        });
      });
    }

    onSubmit(seatAssignments);
  };

  return (
    <div className="seat-modification">
      <h5 className="mb-4">Modify Seat Selection</h5>
      
      {error && (
        <div className="alert alert-danger">
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </div>
      )}

      <SeatSelection
        selectedFlights={booking.selectedFlights}
        passengers={booking.passengerData}
        initialSeatSelections={seatSelections}
        onComplete={handleSeatSelectionComplete}
        onCancel={onCancel}
      />
    </div>
  );
};

export default SeatModification;
