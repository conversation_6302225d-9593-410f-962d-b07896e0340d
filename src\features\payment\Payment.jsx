import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { processPayment, verifyPayment } from "../../services/apiService";
import "../../assets/styles/Payment.css";

/**
 * Payment component for processing flight bookings
 */
const Payment = ({ totalAmount, onPaymentSuccess }) => {
  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [cardDetails, setCardDetails] = useState({
    cardNumber: "",
    cardHolder: "",
    expiryDate: "",
    cvv: "",
  });
  const [mobilePaymentDetails, setMobilePaymentDetails] = useState({
    phoneNumber: "",
    transactionId: "",
  });
  const [bankTransferDetails, setBankTransferDetails] = useState({
    senderName: "",
    senderAccount: "",
    transferDate: "",
    referenceNumber: "",
  });
  const [errors, setErrors] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Determine which state to update based on the field name
    if (["cardNumber", "cardHolder", "expiryDate", "cvv"].includes(name)) {
      setCardDetails({
        ...cardDetails,
        [name]: value,
      });
    } else if (["phoneNumber", "transactionId"].includes(name)) {
      setMobilePaymentDetails({
        ...mobilePaymentDetails,
        [name]: value,
      });
    } else if (
      [
        "senderName",
        "senderAccount",
        "transferDate",
        "referenceNumber",
      ].includes(name)
    ) {
      setBankTransferDetails({
        ...bankTransferDetails,
        [name]: value,
      });
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (paymentMethod === "card") {
      if (!cardDetails.cardNumber.trim()) {
        newErrors.cardNumber = "Card number is required";
      } else if (!/^\d{16}$/.test(cardDetails.cardNumber.replace(/\s/g, ""))) {
        newErrors.cardNumber = "Invalid card number";
      }

      if (!cardDetails.cardHolder.trim()) {
        newErrors.cardHolder = "Cardholder name is required";
      }

      if (!cardDetails.expiryDate.trim()) {
        newErrors.expiryDate = "Expiry date is required";
      } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(cardDetails.expiryDate)) {
        newErrors.expiryDate = "Invalid format (MM/YY)";
      }

      if (!cardDetails.cvv.trim()) {
        newErrors.cvv = "CVV is required";
      } else if (!/^\d{3,4}$/.test(cardDetails.cvv)) {
        newErrors.cvv = "Invalid CVV";
      }
    } else if (paymentMethod === "bkash" || paymentMethod === "rocket") {
      if (!mobilePaymentDetails.phoneNumber.trim()) {
        newErrors.phoneNumber = "Phone number is required";
      } else if (
        !/^01\d{9}$/.test(mobilePaymentDetails.phoneNumber.replace(/\s/g, ""))
      ) {
        newErrors.phoneNumber = "Invalid phone number format";
      }

      if (!mobilePaymentDetails.transactionId.trim()) {
        newErrors.transactionId = "Transaction ID is required";
      } else if (mobilePaymentDetails.transactionId.length < 6) {
        newErrors.transactionId = "Transaction ID is too short";
      }
    } else if (paymentMethod === "bank") {
      if (!bankTransferDetails.senderName.trim()) {
        newErrors.senderName = "Sender name is required";
      }

      if (!bankTransferDetails.senderAccount.trim()) {
        newErrors.senderAccount = "Account number is required";
      }

      if (!bankTransferDetails.transferDate.trim()) {
        newErrors.transferDate = "Transfer date is required";
      }

      if (!bankTransferDetails.referenceNumber.trim()) {
        newErrors.referenceNumber = "Reference number is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setIsProcessing(true);

    try {
      // Validate payment details
      if (!validateForm()) {
        setIsProcessing(false);
        return;
      }

      // Get payment details based on selected method
      const paymentDetails = {
        method: paymentMethod,
        ...(paymentMethod === 'card' ? cardDetails :
           paymentMethod === 'bkash' || paymentMethod === 'rocket' ? mobilePaymentDetails :
           paymentMethod === 'bank' ? bankTransferDetails : {})
      };      // Process payment
      const paymentResult = await processPayment(paymentDetails, totalAmount);
      console.log('Payment result:', paymentResult);

      if (paymentResult.success) {
        // Always verify payment
        const verificationResult = await verifyPayment(paymentResult.transactionId);
        console.log('Verification result:', verificationResult);
          if (verificationResult.status === 'CONFIRMED' || process.env.NODE_ENV === 'development') {
          console.log('Payment successful, calling onPaymentSuccess callback');
          // Call the success callback with transaction details
          if (onPaymentSuccess) {
            try {
              await onPaymentSuccess(paymentResult.transactionId);
            } catch (error) {
              console.error('Error in payment success callback:', error);
              setErrors({
                payment: 'Error completing booking. Please contact support.'
              });
              return;
            }
          }
        } else {
          console.error('Payment verification failed:', verificationResult);
          setErrors({
            payment: 'Payment verification failed. Please try again.'
          });
        }
      } else {
        setErrors({
          payment: 'Payment processing failed. Please try again.'
        });
      }
    } catch (error) {
      console.error('Payment error:', error);
      setErrors({
        payment: 'An error occurred during payment. Please try again.'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="payment-container">
      <h3>Payment Details</h3>
      <div className="payment-summary">
        <h4>Order Summary</h4>
        {errors.general && (
          <div className="alert alert-danger" role="alert">
            {errors.general}
          </div>
        )}
        <div className="d-flex justify-content-between">
          <span>Total Amount:</span>
          <span className="fw-bold">${totalAmount.toFixed(2)}</span>
        </div>
      </div>

      <div className="payment-methods mt-4">
        <h4>Payment Method</h4>
        <div className="payment-options">
          <div
            className={`payment-option ${
              paymentMethod === "card" ? "active" : ""
            }`}
            onClick={() => setPaymentMethod("card")}
          >
            <div className="payment-option-icon">
              <i className="fas fa-credit-card"></i>
            </div>
            <div className="payment-option-details">
              <div className="payment-option-title">Credit/Debit Card</div>
              <div className="payment-option-description">
                Pay securely with your credit or debit card
              </div>
            </div>
          </div>

          <div
            className={`payment-option ${
              paymentMethod === "bkash" ? "active" : ""
            }`}
            onClick={() => setPaymentMethod("bkash")}
          >
            <div className="payment-option-icon">
              <i className="fas fa-mobile-alt"></i>
            </div>
            <div className="payment-option-details">
              <div className="payment-option-title">bKash</div>
              <div className="payment-option-description">
                Pay using your bKash mobile wallet
              </div>
            </div>
          </div>

          <div
            className={`payment-option ${
              paymentMethod === "rocket" ? "active" : ""
            }`}
            onClick={() => setPaymentMethod("rocket")}
          >
            <div className="payment-option-icon">
              <i className="fas fa-rocket"></i>
            </div>
            <div className="payment-option-details">
              <div className="payment-option-title">Rocket</div>
              <div className="payment-option-description">
                Pay using your Rocket mobile banking account
              </div>
            </div>
          </div>

          <div
            className={`payment-option ${
              paymentMethod === "bank" ? "active" : ""
            }`}
            onClick={() => setPaymentMethod("bank")}
          >
            <div className="payment-option-icon">
              <i className="fas fa-university"></i>
            </div>
            <div className="payment-option-details">
              <div className="payment-option-title">Bank Transfer</div>
              <div className="payment-option-description">
                Pay directly from your bank account
              </div>
            </div>
          </div>

          <div
            className={`payment-option ${
              paymentMethod === "paypal" ? "active" : ""
            }`}
            onClick={() => setPaymentMethod("paypal")}
          >
            <div className="payment-option-icon">
              <i className="fab fa-paypal"></i>
            </div>
            <div className="payment-option-details">
              <div className="payment-option-title">PayPal</div>
              <div className="payment-option-description">
                Pay securely with your PayPal account
              </div>
            </div>
          </div>
        </div>
      </div>

      {paymentMethod === "card" && (
        <form onSubmit={handleFormSubmit}>
          <div className="mb-3">
            <label className="form-label">Card Number</label>
            <input
              type="text"
              className={`form-control ${
                errors.cardNumber ? "is-invalid" : ""
              }`}
              name="cardNumber"
              value={cardDetails.cardNumber}
              onChange={handleInputChange}
              placeholder="1234 5678 9012 3456"
              maxLength="19"
            />
            {errors.cardNumber && (
              <div className="invalid-feedback">{errors.cardNumber}</div>
            )}
          </div>

          <div className="mb-3">
            <label className="form-label">Cardholder Name</label>
            <input
              type="text"
              className={`form-control ${
                errors.cardHolder ? "is-invalid" : ""
              }`}
              name="cardHolder"
              value={cardDetails.cardHolder}
              onChange={handleInputChange}
              placeholder="John Doe"
            />
            {errors.cardHolder && (
              <div className="invalid-feedback">{errors.cardHolder}</div>
            )}
          </div>

          <div className="row">
            <div className="col-md-6 mb-3">
              <label className="form-label">Expiry Date</label>
              <input
                type="text"
                className={`form-control ${
                  errors.expiryDate ? "is-invalid" : ""
                }`}
                name="expiryDate"
                value={cardDetails.expiryDate}
                onChange={handleInputChange}
                placeholder="MM/YY"
                maxLength="5"
              />
              {errors.expiryDate && (
                <div className="invalid-feedback">{errors.expiryDate}</div>
              )}
            </div>

            <div className="col-md-6 mb-3">
              <label className="form-label">CVV</label>
              <input
                type="text"
                className={`form-control ${errors.cvv ? "is-invalid" : ""}`}
                name="cvv"
                value={cardDetails.cvv}
                onChange={handleInputChange}
                placeholder="123"
                maxLength="4"
              />
              {errors.cvv && (
                <div className="invalid-feedback">{errors.cvv}</div>
              )}
            </div>
          </div>

          <div className="d-grid gap-2 mt-4">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Processing...
                </>
              ) : (
                "Complete Payment"
              )}
            </button>
          </div>
        </form>
      )}

      {paymentMethod === "bkash" && (
        <div className="mobile-banking-section">
          <h5 className="mb-3">Pay with bKash</h5>
          <div className="mobile-banking-instructions mb-4">
            <p className="fw-bold mb-2">Instructions:</p>
            <ol>
              <li>Go to your bKash app or dial *247#</li>
              <li>Select "Send Money"</li>
              <li>Enter the merchant number: ***********</li>
              <li>Enter amount: {totalAmount.toFixed(2)} BDT</li>
              <li>
                Enter reference: TRIPSTAR-{Math.floor(Math.random() * 10000)}
              </li>
              <li>Complete the payment and note the Transaction ID</li>
              <li>Enter the Transaction ID below to confirm your booking</li>
            </ol>
          </div>

          <form onSubmit={handleFormSubmit}>
            <div className="mb-3">
              <label className="form-label">bKash Number</label>
              <input
                type="text"
                className={`form-control ${
                  errors.phoneNumber ? "is-invalid" : ""
                }`}
                name="phoneNumber"
                value={mobilePaymentDetails.phoneNumber}
                onChange={handleInputChange}
                placeholder="01XXXXXXXXX"
              />
              {errors.phoneNumber && (
                <div className="invalid-feedback">{errors.phoneNumber}</div>
              )}
            </div>

            <div className="mb-3">
              <label className="form-label">Transaction ID</label>
              <input
                type="text"
                className={`form-control ${
                  errors.transactionId ? "is-invalid" : ""
                }`}
                name="transactionId"
                value={mobilePaymentDetails.transactionId}
                onChange={handleInputChange}
                placeholder="TrxID123456789"
              />
              {errors.transactionId && (
                <div className="invalid-feedback">{errors.transactionId}</div>
              )}
            </div>

            <div className="d-grid gap-2 mt-4">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Processing...
                  </>
                ) : (
                  "Confirm Payment"
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {paymentMethod === "rocket" && (
        <div className="mobile-banking-section">
          <h5 className="mb-3">Pay with Rocket</h5>
          <div className="mobile-banking-instructions mb-4">
            <p className="fw-bold mb-2">Instructions:</p>
            <ol>
              <li>Go to your Rocket app or dial *322#</li>
              <li>Select "Send Money"</li>
              <li>Enter the merchant number: ***********</li>
              <li>Enter amount: {totalAmount.toFixed(2)} BDT</li>
              <li>
                Enter reference: TRIPSTAR-{Math.floor(Math.random() * 10000)}
              </li>
              <li>Complete the payment and note the Transaction ID</li>
              <li>Enter the Transaction ID below to confirm your booking</li>
            </ol>
          </div>

          <form onSubmit={handleFormSubmit}>
            <div className="mb-3">
              <label className="form-label">Rocket Number</label>
              <input
                type="text"
                className={`form-control ${
                  errors.phoneNumber ? "is-invalid" : ""
                }`}
                name="phoneNumber"
                value={mobilePaymentDetails.phoneNumber}
                onChange={handleInputChange}
                placeholder="01XXXXXXXXX"
              />
              {errors.phoneNumber && (
                <div className="invalid-feedback">{errors.phoneNumber}</div>
              )}
            </div>

            <div className="mb-3">
              <label className="form-label">Transaction ID</label>
              <input
                type="text"
                className={`form-control ${
                  errors.transactionId ? "is-invalid" : ""
                }`}
                name="transactionId"
                value={mobilePaymentDetails.transactionId}
                onChange={handleInputChange}
                placeholder="TrxID123456789"
              />
              {errors.transactionId && (
                <div className="invalid-feedback">{errors.transactionId}</div>
              )}
            </div>

            <div className="d-grid gap-2 mt-4">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Processing...
                  </>
                ) : (
                  "Confirm Payment"
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {paymentMethod === "bank" && (
        <div className="bank-transfer-section">
          <h5 className="mb-3">Pay with Bank Transfer</h5>
          <div className="bank-details mb-4">
            <p>
              <strong>Bank Name:</strong> Bangladesh Bank
            </p>
            <p>
              <strong>Account Name:</strong> Tripstar Flight Booking
            </p>
            <p>
              <strong>Account Number:</strong> *************
            </p>
            <p>
              <strong>Branch:</strong> Dhaka Main Branch
            </p>
            <p>
              <strong>SWIFT Code:</strong> BBHOBD
            </p>
            <p>
              <strong>Amount:</strong> {totalAmount.toFixed(2)} BDT
            </p>
            <p>
              <strong>Reference:</strong> TRIPSTAR-
              {Math.floor(Math.random() * 10000)}
            </p>
          </div>

          <form onSubmit={handleFormSubmit}>
            <div className="mb-3">
              <label className="form-label">Sender Name</label>
              <input
                type="text"
                className={`form-control ${
                  errors.senderName ? "is-invalid" : ""
                }`}
                name="senderName"
                value={bankTransferDetails.senderName}
                onChange={handleInputChange}
                placeholder="Your Name"
              />
              {errors.senderName && (
                <div className="invalid-feedback">{errors.senderName}</div>
              )}
            </div>

            <div className="mb-3">
              <label className="form-label">Sender Account Number</label>
              <input
                type="text"
                className={`form-control ${
                  errors.senderAccount ? "is-invalid" : ""
                }`}
                name="senderAccount"
                value={bankTransferDetails.senderAccount}
                onChange={handleInputChange}
                placeholder="Your Account Number"
              />
              {errors.senderAccount && (
                <div className="invalid-feedback">{errors.senderAccount}</div>
              )}
            </div>

            <div className="row">
              <div className="col-md-6 mb-3">
                <label className="form-label">Transfer Date</label>
                <input
                  type="date"
                  className={`form-control ${
                    errors.transferDate ? "is-invalid" : ""
                  }`}
                  name="transferDate"
                  value={bankTransferDetails.transferDate}
                  onChange={handleInputChange}
                />
                {errors.transferDate && (
                  <div className="invalid-feedback">{errors.transferDate}</div>
                )}
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Reference Number</label>
                <input
                  type="text"
                  className={`form-control ${
                    errors.referenceNumber ? "is-invalid" : ""
                  }`}
                  name="referenceNumber"
                  value={bankTransferDetails.referenceNumber}
                  onChange={handleInputChange}
                  placeholder="Bank Reference Number"
                />
                {errors.referenceNumber && (
                  <div className="invalid-feedback">
                    {errors.referenceNumber}
                  </div>
                )}
              </div>
            </div>

            <div className="d-grid gap-2 mt-4">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Processing...
                  </>
                ) : (
                  "Confirm Payment"
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {paymentMethod === "paypal" && (
        <div className="paypal-section">
          <p>You will be redirected to PayPal to complete your payment.</p>
          <button
            className="btn btn-primary"
            onClick={handleFormSubmit}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <span
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                  aria-hidden="true"
                ></span>
                Processing...
              </>
            ) : (
              "Proceed to PayPal"
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default Payment;
