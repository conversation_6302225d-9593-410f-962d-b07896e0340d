import React, { useState } from "react";

export const PaymentForm = ({ totalAmount, onPaymentSuccess }) => {
  const [paymentDetails, setPaymentDetails] = useState({
    cardNumber: "",
    cardholderName: "",
    expiryDate: "",
    cvv: "",
    promoCode: "",
  });
  const [errors, setErrors] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);

  const validatePaymentDetails = () => {
    const newErrors = {};

    // Card number validation (using <PERSON>hn algorithm)
    if (!isValidCardNumber(paymentDetails.cardNumber)) {
      newErrors.cardNumber = "Invalid card number";
    }

    // CVV validation
    if (!/^\d{3,4}$/.test(paymentDetails.cvv)) {
      newErrors.cvv = "Invalid CVV";
    }

    // Expiry date validation
    if (!paymentDetails.expiryDate) {
      newErrors.expiryDate = "Expiry date is required";
    } else {
      const [month, year] = paymentDetails.expiryDate.split("/");
      const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
      if (expiry < new Date()) {
        newErrors.expiryDate = "Card has expired";
      }
    }

    // Cardholder name validation
    if (!paymentDetails.cardholderName.trim()) {
      newErrors.cardholderName = "Cardholder name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validatePaymentDetails()) return;

    setIsProcessing(true);
    try {
      // Validate promo code if provided
      if (paymentDetails.promoCode) {
        const promoValidation = await validatePromoCode(
          paymentDetails.promoCode
        );
        if (!promoValidation.valid) {
          setErrors({ promoCode: promoValidation.message });
          setIsProcessing(false);
          return;
        }
      }

      // Process payment
      const paymentResult = await processPayment({
        ...paymentDetails,
        amount: totalAmount,
      });

      if (paymentResult.success) {
        onPaymentSuccess(paymentResult.transactionId);
      } else {
        setErrors({
          payment: paymentResult.message || "Payment failed. Please try again.",
        });
      }
    } catch (error) {
      setErrors({
        payment:
          "An error occurred while processing your payment. Please try again.",
      });
      console.error("Payment processing error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="payment-form">
      <h4>Payment Details</h4>
      <div className="payment-methods">
        <label>
          <input
            type="radio"
            checked={paymentMethod === "credit"}
            onChange={() => setPaymentMethod("credit")}
          />
          Credit Card
        </label>
        <label>
          <input
            type="radio"
            checked={paymentMethod === "mobile"}
            onChange={() => setPaymentMethod("mobile")}
          />
          Mobile Payment
        </label>
      </div>

      {paymentMethod === "credit" && (
        <div className="payment-details">
          {errors.payment && (
            <div className="alert alert-danger">{errors.payment}</div>
          )}
          <div className="form-group">
            <label>Card Number</label>
            <input
              type="text"
              value={paymentDetails.cardNumber}
              onChange={(e) =>
                setPaymentDetails({
                  ...paymentDetails,
                  cardNumber: e.target.value,
                })
              }
              placeholder="1234 5678 9012 3456"
              required
            />
          </div>
          <div className="form-group">
            <label>Cardholder Name</label>
            <input
              type="text"
              value={paymentDetails.cardholderName}
              onChange={(e) =>
                setPaymentDetails({
                  ...paymentDetails,
                  cardholderName: e.target.value,
                })
              }
              placeholder="John Doe"
              required
            />
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Expiry Date</label>
              <input
                type="text"
                value={paymentDetails.expiryDate}
                onChange={(e) =>
                  setPaymentDetails({
                    ...paymentDetails,
                    expiryDate: e.target.value,
                  })
                }
                placeholder="MM/YY"
                required
              />
            </div>
            <div className="form-group">
              <label>CVV</label>
              <input
                type="text"
                value={paymentDetails.cvv}
                onChange={(e) =>
                  setPaymentDetails({
                    ...paymentDetails,
                    cvv: e.target.value,
                  })
                }
                placeholder="123"
                required
              />
            </div>
          </div>
          <div className="form-group">
            <h5>Total Amount: ${totalAmount}</h5>
          </div>
          <button type="submit" className="btn btn-primary">
            Confirm Payment
          </button>
        </div>
      )}
    </form>
  );
};
